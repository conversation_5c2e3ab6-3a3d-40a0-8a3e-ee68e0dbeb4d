package com.example.tikoppij.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.example.tikoppij.model.DownloadModel
import com.example.tikoppij.model.VideoModel
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

/**
 * 下载历史管理Repository接口
 */
interface DownloadRepository {
    /**
     * 获取下载历史列表
     */
    fun getDownloadList(): Flow<List<DownloadModel>>
    
    /**
     * 添加下载记录
     */
    suspend fun addDownload(video: VideoModel, fileName: String, filePath: String)
    
    /**
     * 移除下载记录
     */
    suspend fun removeDownload(videoId: String)
    
    /**
     * 清空下载历史
     */
    suspend fun clearDownloads()
}

/**
 * 下载历史管理Repository实现
 */
class DownloadRepositoryImpl private constructor(
    context: Context
) : DownloadRepository {
    
    private val appContext = context.applicationContext
    
    companion object {
        @Volatile
        private var INSTANCE: DownloadRepositoryImpl? = null
        
        fun getInstance(context: Context): DownloadRepositoryImpl {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DownloadRepositoryImpl(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        // DataStore扩展
        private val Context.downloadDataStore: DataStore<Preferences> by preferencesDataStore(name = "downloads")
        
        // PreferencesKeys
        private val DOWNLOAD_LIST_KEY = stringPreferencesKey("download_list")
    }
    
    private val gson = Gson()
    private val downloadListType = object : TypeToken<List<DownloadModel>>() {}.type
    
    override fun getDownloadList(): Flow<List<DownloadModel>> {
        return appContext.downloadDataStore.data.map { preferences ->
            val downloadJson = preferences[DOWNLOAD_LIST_KEY] ?: "[]"
            try {
                val downloads = gson.fromJson<List<DownloadModel>>(downloadJson, downloadListType) ?: emptyList()
                // 按下载时间倒序排列（最新的在前面）
                downloads.sortedByDescending { it.downloadTime }
            } catch (_: Exception) {
                emptyList()
            }
        }
    }
    
    override suspend fun addDownload(video: VideoModel, fileName: String, filePath: String) {
        appContext.downloadDataStore.edit { preferences ->
            val currentDownloads = getDownloadListSync(preferences)
            
            // 更严格的去重检查：检查videoId或fileName是否已存在
            val isDuplicate = currentDownloads.any { 
                it.videoId == video.video_id || it.fileName == fileName 
            }
            
            if (isDuplicate) {
                return@edit // 已存在，不重复添加
            }
            
            val newDownload = DownloadModel(
                videoId = video.video_id,
                url = video.url,
                width = video.width,
                height = video.height,
                category = video.Category,
                fileName = fileName,
                filePath = filePath,
                downloadTime = System.currentTimeMillis()
            )
            
            val updatedDownloads = currentDownloads + newDownload
            preferences[DOWNLOAD_LIST_KEY] = gson.toJson(updatedDownloads)
        }
    }
    
    override suspend fun removeDownload(videoId: String) {
        appContext.downloadDataStore.edit { preferences ->
            val currentDownloads = getDownloadListSync(preferences)
            val updatedDownloads = currentDownloads.filter { it.videoId != videoId }
            preferences[DOWNLOAD_LIST_KEY] = gson.toJson(updatedDownloads)
        }
    }
    
    override suspend fun clearDownloads() {
        appContext.downloadDataStore.edit { preferences ->
            preferences[DOWNLOAD_LIST_KEY] = "[]"
        }
    }
    
    /**
     * 同步获取下载历史列表（用于DataStore edit块内）
     */
    private fun getDownloadListSync(preferences: Preferences): List<DownloadModel> {
        val downloadJson = preferences[DOWNLOAD_LIST_KEY] ?: "[]"
        return try {
            gson.fromJson<List<DownloadModel>>(downloadJson, downloadListType) ?: emptyList()
        } catch (_: Exception) {
            emptyList()
        }
    }
} 