1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.tikoppij4"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 权限声明 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:8:5-68
14-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:8:22-65
15
16    <!-- 存储权限 - 仅Android 9及以下需要 -->
17    <uses-permission
17-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:11:5-12:38
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:11:22-78
19        android:maxSdkVersion="28" />
19-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:12:9-35
20
21    <permission
21-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
22        android:name="com.example.tikoppij4.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
22-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
23        android:protectionLevel="signature" />
23-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
24
25    <uses-permission android:name="com.example.tikoppij4.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
25-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
25-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
26
27    <application
27-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:14:5-51:19
28        android:name="com.example.tikoppij.MyApplication"
28-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:15:9-38
29        android:allowBackup="true"
29-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:16:9-35
30        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
30-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
31        android:dataExtractionRules="@xml/data_extraction_rules"
31-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:17:9-65
32        android:debuggable="true"
33        android:extractNativeLibs="false"
34        android:fullBackupContent="@xml/backup_rules"
34-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:18:9-54
35        android:icon="@mipmap/ic_launcher"
35-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:19:9-43
36        android:label="@string/app_name"
36-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:20:9-41
37        android:roundIcon="@mipmap/ic_launcher_round"
37-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:21:9-54
38        android:supportsRtl="true"
38-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:22:9-35
39        android:testOnly="true"
40        android:theme="@android:style/Theme.Material.Light.NoActionBar"
40-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:23:9-72
41        android:usesCleartextTraffic="true" >
41-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:24:9-44
42
43        <!-- 配置App Startup -->
44        <provider
45            android:name="androidx.startup.InitializationProvider"
45-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:29:13-67
46            android:authorities="com.example.tikoppij4.androidx-startup"
46-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:30:13-68
47            android:exported="false" >
47-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:31:13-37
48
49            <!-- 注册统一初始化器 -->
50            <meta-data
50-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:34:13-36:52
51                android:name="com.example.tikoppij.initializer.AppInitializer"
51-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:35:17-79
52                android:value="androidx.startup" />
52-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:36:17-49
53            <meta-data
53-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\5ed9fe968b01500d2464018fae24ae8e\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
54                android:name="androidx.emoji2.text.EmojiCompatInitializer"
54-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\5ed9fe968b01500d2464018fae24ae8e\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
55                android:value="androidx.startup" />
55-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\5ed9fe968b01500d2464018fae24ae8e\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
56            <meta-data
56-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\ede2c9572e5ad6a65b316ccee8fd469e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
57                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
57-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\ede2c9572e5ad6a65b316ccee8fd469e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
58                android:value="androidx.startup" />
58-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\ede2c9572e5ad6a65b316ccee8fd469e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
59            <meta-data
59-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
60                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
60-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
61                android:value="androidx.startup" />
61-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
62        </provider>
63
64        <activity
64-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:39:9-50:20
65            android:name="com.example.tikoppij.MainActivity"
65-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:40:13-41
66            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden|smallestScreenSize"
66-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:42:13-106
67            android:exported="true"
67-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:41:13-36
68            android:theme="@android:style/Theme.Material.Light.NoActionBar"
68-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:43:13-76
69            android:windowSoftInputMode="adjustResize" >
69-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:44:13-55
70            <intent-filter>
70-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:45:13-49:29
71                <action android:name="android.intent.action.MAIN" />
71-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:46:17-69
71-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:46:25-66
72
73                <category android:name="android.intent.category.LAUNCHER" />
73-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:48:17-77
73-->C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:48:27-74
74            </intent-filter>
75        </activity>
76
77        <receiver
77-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
78            android:name="androidx.profileinstaller.ProfileInstallReceiver"
78-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
79            android:directBootAware="false"
79-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
80            android:enabled="true"
80-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
81            android:exported="true"
81-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
82            android:permission="android.permission.DUMP" >
82-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
84                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
84-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
84-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
87                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
87-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
87-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
88            </intent-filter>
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
90                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
90-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
90-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
91            </intent-filter>
92            <intent-filter>
92-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
93                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
93-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
93-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
94            </intent-filter>
95        </receiver>
96    </application>
97
98</manifest>
