package com.example.tikoppij.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.tikoppij.R
import com.example.tikoppij.data.DownloadRepositoryImpl
import com.example.tikoppij.model.VideoModel
import com.example.tikoppij.utils.CustomDownloadManager
import com.example.tikoppij.utils.PermissionHandler
import com.example.tikoppij.utils.PermissionUtils
import com.example.tikoppij.utils.ShareUtils
import kotlinx.coroutines.launch
import kotlin.math.roundToInt

/**
 * 通用视频控制按钮组件
 * 用于主页、收藏页面、播放页面的右下角功能按钮
 */
@Composable
fun VideoControlButtons(
    modifier: Modifier = Modifier,
    currentVideo: VideoModel? = null,
    isFavorite: Boolean = false,
    onFavoriteClick: () -> Unit = {},
    onDownloadClick: () -> Unit = {},
    onShareClick: () -> Unit = {},
    onMenuClick: (() -> Unit)? = null,
    onToggleBottomBarVisibility: (() -> Unit)? = null,
    isBottomBarVisible: Boolean = true,
    showToggleButton: Boolean = true,
    showMenuButton: Boolean = true
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    
    // 下载状态
    val downloadState by CustomDownloadManager.downloadState.collectAsState()
    
    var showDownloadDialog by remember { mutableStateOf(false) }
    var showProgressDialog by remember { mutableStateOf(false) }
    var showResultDialog by remember { mutableStateOf(false) }
    var showPermissionDialog by remember { mutableStateOf(false) }
    var downloadResult by remember { mutableStateOf<String?>(null) }
    var requestPermissionFunction by remember { mutableStateOf<(() -> Unit)?>(null) }
    
    // 权限处理器
    PermissionHandler(
        onPermissionGranted = {
            // 权限获取成功，开始下载
            showDownloadDialog = true
            showPermissionDialog = false
        },
        onPermissionDenied = {
            // 权限被拒绝
            downloadResult = "下载失败：需要存储权限才能保存视频文件。\n\n请在设置中手动授予存储权限后重试。"
            showResultDialog = true
            showPermissionDialog = false
        }
    ) { requestPermission ->
        requestPermissionFunction = requestPermission
    }
    
    Column(
        modifier = modifier
    ) {
        // 收藏按钮
        VideoControlButton(
            onClick = onFavoriteClick
        ) {
            Icon(
                painter = painterResource(id = if (isFavorite) R.drawable.ic_favorite else R.drawable.ic_favorite_border),
                contentDescription = null,
                tint = if (isFavorite) Color.Red else Color.White,
                modifier = Modifier.size(24.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 下载按钮
        VideoControlButton(
            onClick = { 
                if (!downloadState.isDownloading) {
                    // 首先检查权限
                    if (PermissionUtils.hasDownloadPermission(context)) {
                        showDownloadDialog = true
                    } else {
                        showPermissionDialog = true
                    }
                }
                onDownloadClick()
            }
        ) {
            if (downloadState.isDownloading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    color = Color.White,
                    strokeWidth = 2.dp,
                    progress = { downloadState.progress }
                )
            } else {
                Icon(
                    painter = painterResource(id = R.drawable.ic_download),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 分享按钮
        VideoControlButton(
            onClick = { 
                currentVideo?.let { video ->
                    ShareUtils.shareVideoInfo(context, video.video_id, video.url)
                }
                onShareClick()
            }
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_share),
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }
        
        // 隐藏/显示底部导航栏按钮（仅在主页显示）
        if (showToggleButton && onToggleBottomBarVisibility != null) {
            Spacer(modifier = Modifier.height(12.dp))
            
            VideoControlButton(
                onClick = onToggleBottomBarVisibility
            ) {
                Icon(
                    painter = painterResource(id = if (isBottomBarVisible) R.drawable.ic_visibility_off else R.drawable.ic_visibility),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
        
        // 菜单按钮
        if (showMenuButton && onMenuClick != null) {
            Spacer(modifier = Modifier.height(12.dp))
            
            VideoControlButton(
                onClick = onMenuClick
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_menu),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
    
    // 权限请求对话框
    if (showPermissionDialog) {
        AlertDialog(
            onDismissRequest = { 
                showPermissionDialog = false 
            },
            title = { 
                Text(
                    text = "需要存储权限",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                ) 
            },
            text = { 
                Text(
                    text = "为了保存视频到您的设备，我们需要访问存储权限。\n\n点击「授予权限」来允许应用保存视频文件。",
                    fontSize = 14.sp,
                    lineHeight = 20.sp
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        requestPermissionFunction?.invoke()
                    },
                    colors = ButtonDefaults.textButtonColors(
                        containerColor = Color.Black,
                        contentColor = Color.White
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text("授予权限")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showPermissionDialog = false },
                    colors = ButtonDefaults.textButtonColors(
                        containerColor = Color.Transparent,
                        contentColor = Color.Gray
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text("取消")
                }
            }
        )
    }
    
    // 下载确认对话框
    if (showDownloadDialog) {
        AlertDialog(
            onDismissRequest = { 
                showDownloadDialog = false 
            },
            title = { 
                Text(
                    text = "下载视频",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                ) 
            },
            text = { 
                Text(
                    text = "确定要下载这个视频吗？\n\n视频将保存到 DCIM/tikapp 文件夹，您可以在相册或文件管理器中找到。",
                    fontSize = 14.sp,
                    lineHeight = 20.sp
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        currentVideo?.let { video ->
                            showDownloadDialog = false
                            showProgressDialog = true
                            
                            coroutineScope.launch {
                                try {
                                    val result = CustomDownloadManager.downloadVideo(
                                        context = context,
                                        videoUrl = video.url
                                    )
                                    
                                    showProgressDialog = false
                                    
                                    when (result) {
                                        is CustomDownloadManager.DownloadResult.Success -> {
                                            // 保存下载记录到DownloadRepository
                                            try {
                                                val downloadRepository = DownloadRepositoryImpl.getInstance(context)
                                                downloadRepository.addDownload(
                                                    video = video,
                                                    fileName = result.fileName,
                                                    filePath = result.filePath
                                                )
                                                downloadResult = "下载成功！\n\n文件保存至：DCIM/tikapp/${result.fileName}\n\n您可以在相册或文件管理器中查看。"
                                            } catch (e: Exception) {
                                                downloadResult = "下载成功，但保存记录失败：${e.message}"
                                            }
                                        }
                                        is CustomDownloadManager.DownloadResult.Error -> {
                                            downloadResult = "下载失败：${result.message}"
                                        }
                                    }
                                    
                                    showResultDialog = true
                                } catch (e: Exception) {
                                    showProgressDialog = false
                                    downloadResult = "下载异常：${e.message}"
                                    showResultDialog = true
                                }
                            }
                        }
                    },
                    colors = ButtonDefaults.textButtonColors(
                        containerColor = Color.Black,
                        contentColor = Color.White
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text("确认下载")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDownloadDialog = false },
                    colors = ButtonDefaults.textButtonColors(
                        containerColor = Color.Transparent,
                        contentColor = Color.Gray
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text("取消")
                }
            }
        )
    }
    
    // 下载进度对话框
    if (showProgressDialog) {
        AlertDialog(
            onDismissRequest = { 
                // 防止用户意外关闭进度对话框
            },
            title = { 
                Text(
                    text = "正在下载",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                ) 
            },
            text = { 
                Column {
                    Text(
                        text = "文件名：${downloadState.fileName}",
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 进度条
                    LinearProgressIndicator(
                        progress = { downloadState.progress },
                        modifier = Modifier.fillMaxWidth(),
                        color = Color.Black,
                        trackColor = Color.Gray.copy(alpha = 0.3f)
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 进度文字
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "${(downloadState.progress * 100).roundToInt()}%",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                        
                        if (downloadState.totalBytes > 0) {
                            Text(
                                text = "${formatBytes(downloadState.downloadedBytes)} / ${formatBytes(downloadState.totalBytes)}",
                                fontSize = 12.sp,
                                color = Color.Gray
                            )
                        }
                    }
                    
                    // 错误信息
                    downloadState.errorMessage?.let { error ->
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = error,
                            fontSize = 12.sp,
                            color = Color.Red
                        )
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        CustomDownloadManager.cancelDownload()
                        showProgressDialog = false
                        downloadResult = "下载已取消"
                        showResultDialog = true
                    },
                    colors = ButtonDefaults.textButtonColors(
                        containerColor = Color.Red.copy(alpha = 0.1f),
                        contentColor = Color.Red
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text("取消下载")
                }
            }
        )
    }
    
    // 下载结果对话框
    if (showResultDialog && downloadResult != null) {
        AlertDialog(
            onDismissRequest = { 
                showResultDialog = false
                downloadResult = null
                CustomDownloadManager.resetDownloadState()
            },
            title = { 
                Text(
                    text = if (downloadResult!!.startsWith("下载成功")) "下载完成" else "下载失败",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = if (downloadResult!!.startsWith("下载成功")) Color.Green else Color.Red
                ) 
            },
            text = { 
                Text(
                    text = downloadResult!!,
                    fontSize = 14.sp,
                    lineHeight = 20.sp
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showResultDialog = false
                        downloadResult = null
                        CustomDownloadManager.resetDownloadState()
                    },
                    colors = ButtonDefaults.textButtonColors(
                        containerColor = Color.Black,
                        contentColor = Color.White
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text("确定")
                }
            }
        )
    }
}

/**
 * 单个视频控制按钮
 * 优化为黑色背景
 */
@Composable
private fun VideoControlButton(
    onClick: () -> Unit,
    content: @Composable () -> Unit
) {
    Box(
        modifier = Modifier
            .size(48.dp)
            .clip(RoundedCornerShape(24.dp))
            .background(Color.Black.copy(alpha = 0.6f))
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        content()
    }
}

/**
 * 格式化字节数为可读格式
 */
private fun formatBytes(bytes: Long): String {
    return when {
        bytes < 1024 -> "${bytes}B"
        bytes < 1024 * 1024 -> "${(bytes / 1024f).roundToInt()}KB"
        bytes < 1024 * 1024 * 1024 -> "${(bytes / (1024f * 1024f)).roundToInt()}MB"
        else -> "${(bytes / (1024f * 1024f * 1024f)).roundToInt()}GB"
    }
} 