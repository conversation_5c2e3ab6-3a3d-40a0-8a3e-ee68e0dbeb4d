-- Merging decision tree log ---
provider#androidx.startup.InitializationProvider
INJECTED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:28:9-37:20
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\5ed9fe968b01500d2464018fae24ae8e\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\5ed9fe968b01500d2464018fae24ae8e\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\ede2c9572e5ad6a65b316ccee8fd469e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\ede2c9572e5ad6a65b316ccee8fd469e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.2.0] D:\SDK\.gradle\caches\8.14\transforms\4d4a17d93d8d3c41e97371df5a6ce4d5\transformed\startup-runtime-1.2.0\AndroidManifest.xml:24:9-28:34
MERGED from [androidx.startup:startup-runtime:1.2.0] D:\SDK\.gradle\caches\8.14\transforms\4d4a17d93d8d3c41e97371df5a6ce4d5\transformed\startup-runtime-1.2.0\AndroidManifest.xml:24:9-28:34
	tools:node
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:32:13-31
	android:authorities
		INJECTED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:30:13-68
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:31:13-37
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:29:13-67
manifest
ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:2:1-53:12
INJECTED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:2:1-53:12
INJECTED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:2:1-53:12
INJECTED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:2:1-53:12
MERGED from [io.insert-koin:koin-androidx-compose:3.5.0] D:\SDK\.gradle\caches\8.14\transforms\0940e65518eb5585f59dac9673d315ca\transformed\koin-androidx-compose-3.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [io.insert-koin:koin-android:3.5.0] D:\SDK\.gradle\caches\8.14\transforms\cfab10a8cc8eac2554fd5869fec51426\transformed\koin-android-3.5.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\SDK\.gradle\caches\8.14\transforms\5e6ad5cfd7f59010c4cf09016f94c570\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] D:\SDK\.gradle\caches\8.14\transforms\578a30a6859b830ed2d96a2775f60551\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.6.1] D:\SDK\.gradle\caches\8.14\transforms\ee999f646d3c4d42c199e07d638b68f0\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] D:\SDK\.gradle\caches\8.14\transforms\7e28f7a15e7f33ae1283e12fcaeec20d\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\0f7de0ab20d2d4aa3f16d22ed4d51da9\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\d5ca53df2f4de933a04106af8059f679\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\53f51a02c7ff37d81e21ef5fdaeeb7a9\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] D:\SDK\.gradle\caches\8.14\transforms\78314523cef4db59a8212cb4684bc3df\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource-okhttp:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\eda307ef0a619080f1b352269a59a063\transformed\media3-datasource-okhttp-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\2dd813087725868c007175ace6408115\transformed\media3-extractor-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\c3fe6f3cfd0e972d23acbbfc417ab96c\transformed\media3-container-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\a4146dbf6ba7b1142a57db9d1c131377\transformed\media3-datasource-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\bb2d7ea3a24fa745dfa140d5be189e97\transformed\media3-decoder-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\bf810b01cfcb416f852f04018d32ab57\transformed\media3-database-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\ce241f4f00408d6d982f97b8bf43d28b\transformed\media3-common-1.7.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\5ba4a6657edbb78623de310dd1eca8ca\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] D:\SDK\.gradle\caches\8.14\transforms\399559d37d4f9952e3583715be17e0f6\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\a418d9ee54fd83df91c25a130e4076d7\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\554a5d72ea9cae8c2cb92b51029dbc26\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\206c8a9644fba236f1db4793767b7fef\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\d44447578f55ada513db334036579163\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\f9d7a7c2b70351e51375561b594e5917\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\1c011daab12685aa09203682acc8e0f9\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\04dbf29eb04df97d8c6c0277c6c15c7d\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\9e520c6756c32abb810b710b15b2c662\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\f16851d60c41318d8943e458bbeca9a2\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\63f854a8edbd25820eac56875abaf822\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\c70752de305f8b96513a22db66f735b0\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\55a96b744d333b0991aced6d85bce14b\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\b491bcf5dd68b56d0a2b4a50410efb62\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\SDK\.gradle\caches\8.14\transforms\d91307c135b9d7e3689c4d1fe553fd42\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\SDK\.gradle\caches\8.14\transforms\a442b737241294134111261aa83b3389\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\SDK\.gradle\caches\8.14\transforms\bd37db9f7ef03323382ae9af04943002\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\51b41db2807dde6044ff5d0449c971f3\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\edb26f726f53caecf9f8a473a343f5c0\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\db2e86af43ceb3b81be2ee6d48d402ff\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\7ee722d410a53133d25228fe9f9df233\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\5ed9fe968b01500d2464018fae24ae8e\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\ede2c9572e5ad6a65b316ccee8fd469e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\e7d1e06eb01e8974c785ff6974f0437a\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\9c5d8cd33e427e7de704ba15bc178278\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\92ac6a56d7390863d6b4f2beccaba29a\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\f7b7b12584c2fe6ad14ffff97c524bca\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\16e3f2687906432544aa0043af7a5bc5\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\SDK\.gradle\caches\8.14\transforms\f26c8b0221aa77307cc910cc94957aff\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] D:\SDK\.gradle\caches\8.14\transforms\dfbdb30de0961759c1a4169be9c68cad\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\SDK\.gradle\caches\8.14\transforms\35e7c7b6047155d20b6dcb713663c4cd\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\935e41b50c677756c00da7e6f2df7c22\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\d7a49cf5bcfa8acdf4c990d2ac96b764\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\fd110c8bfe9179bf0260f30f0788b317\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\901ede9133fe3d4372b4955a87daedf9\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\473191ebce6706d9bb70d3b6353c5b28\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\f55466f566e53e0e2bbdf1bb547ffaca\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] D:\SDK\.gradle\caches\8.14\transforms\22d66dc3e4353d95ada7e63cb6ce08d2\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\f39683438b1626831bb233ed2cba9a31\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] D:\SDK\.gradle\caches\8.14\transforms\e86fad15546fb9302ac2d731fb44fc17\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] D:\SDK\.gradle\caches\8.14\transforms\da43022322a5a860f421f349ce046cac\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\9a6eec2b4bc1b0987c0d4609e6984d48\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\01b9ee1fe1ea2e0008327a7aa7d8eead\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-core-android:1.1.7] D:\SDK\.gradle\caches\8.14\transforms\09ff9ef8b1bf55042db9fb3aaf5c562f\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] D:\SDK\.gradle\caches\8.14\transforms\d2fd20bbf8ba36ec95faa48a7be0bdc9\transformed\datastore-preferences-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.7] D:\SDK\.gradle\caches\8.14\transforms\12c9267fdaa49f9e9f1e9c661d516b85\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] D:\SDK\.gradle\caches\8.14\transforms\838879e34554298736fd84f6b87f1be2\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\41a2cda2f46b7cd00f8f1f6af5d99772\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\e1735ce06065d6ef67994e400b57f168\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\SDK\.gradle\caches\8.14\transforms\efb014f164a5d3da771a81ddfa7c65cf\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\87425f190765f41654b5b587646f2adb\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.14\transforms\ad613a999c5c8ee3883bbe6bfcfa78ca\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.2.0] D:\SDK\.gradle\caches\8.14\transforms\4d4a17d93d8d3c41e97371df5a6ce4d5\transformed\startup-runtime-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\SDK\.gradle\caches\8.14\transforms\f6976502fc9c64795fa15ec6ab5346f9\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.tracing:tracing:1.2.0] D:\SDK\.gradle\caches\8.14\transforms\cf91559e9d7e2242136b065e3be61cd1\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\3a4d47ac81379c954353de23ae92efb5\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\791bac657cca89c17f064ee8c56eb28b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\SDK\.gradle\caches\8.14\transforms\93b3554daae3d7b3a889c209eb1bb130\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.media3:media3-common:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\ce241f4f00408d6d982f97b8bf43d28b\transformed\media3-common-1.7.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\ce241f4f00408d6d982f97b8bf43d28b\transformed\media3-common-1.7.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\5ba4a6657edbb78623de310dd1eca8ca\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\5ba4a6657edbb78623de310dd1eca8ca\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:8:5-68
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:8:22-65
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:11:5-12:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:12:9-35
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:11:22-78
application
ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:14:5-51:19
INJECTED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:14:5-51:19
MERGED from [io.insert-koin:koin-android:3.5.0] D:\SDK\.gradle\caches\8.14\transforms\cfab10a8cc8eac2554fd5869fec51426\transformed\koin-android-3.5.0\AndroidManifest.xml:9:5-20
MERGED from [io.insert-koin:koin-android:3.5.0] D:\SDK\.gradle\caches\8.14\transforms\cfab10a8cc8eac2554fd5869fec51426\transformed\koin-android-3.5.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\5ed9fe968b01500d2464018fae24ae8e\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\5ed9fe968b01500d2464018fae24ae8e\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\ede2c9572e5ad6a65b316ccee8fd469e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\ede2c9572e5ad6a65b316ccee8fd469e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.14\transforms\ad613a999c5c8ee3883bbe6bfcfa78ca\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.14\transforms\ad613a999c5c8ee3883bbe6bfcfa78ca\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.2.0] D:\SDK\.gradle\caches\8.14\transforms\4d4a17d93d8d3c41e97371df5a6ce4d5\transformed\startup-runtime-1.2.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.startup:startup-runtime:1.2.0] D:\SDK\.gradle\caches\8.14\transforms\4d4a17d93d8d3c41e97371df5a6ce4d5\transformed\startup-runtime-1.2.0\AndroidManifest.xml:23:5-29:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:22:9-35
	android:label
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:20:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:18:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:21:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:25:9-29
	android:icon
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:19:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:16:9-35
	android:theme
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:23:9-72
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:17:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:24:9-44
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:15:9-38
activity#com.example.tikoppij.MainActivity
ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:39:9-50:20
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:44:13-55
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:41:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:42:13-106
	android:theme
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:43:13-76
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:40:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:45:13-49:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:46:17-69
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:46:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:48:17-77
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:48:27-74
meta-data#com.example.tikoppij.initializer.AppInitializer
ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:36:17-49
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml:35:17-79
uses-sdk
INJECTED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml
MERGED from [io.insert-koin:koin-androidx-compose:3.5.0] D:\SDK\.gradle\caches\8.14\transforms\0940e65518eb5585f59dac9673d315ca\transformed\koin-androidx-compose-3.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [io.insert-koin:koin-androidx-compose:3.5.0] D:\SDK\.gradle\caches\8.14\transforms\0940e65518eb5585f59dac9673d315ca\transformed\koin-androidx-compose-3.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [io.insert-koin:koin-android:3.5.0] D:\SDK\.gradle\caches\8.14\transforms\cfab10a8cc8eac2554fd5869fec51426\transformed\koin-android-3.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [io.insert-koin:koin-android:3.5.0] D:\SDK\.gradle\caches\8.14\transforms\cfab10a8cc8eac2554fd5869fec51426\transformed\koin-android-3.5.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\SDK\.gradle\caches\8.14\transforms\5e6ad5cfd7f59010c4cf09016f94c570\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\SDK\.gradle\caches\8.14\transforms\5e6ad5cfd7f59010c4cf09016f94c570\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] D:\SDK\.gradle\caches\8.14\transforms\578a30a6859b830ed2d96a2775f60551\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] D:\SDK\.gradle\caches\8.14\transforms\578a30a6859b830ed2d96a2775f60551\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] D:\SDK\.gradle\caches\8.14\transforms\ee999f646d3c4d42c199e07d638b68f0\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] D:\SDK\.gradle\caches\8.14\transforms\ee999f646d3c4d42c199e07d638b68f0\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] D:\SDK\.gradle\caches\8.14\transforms\7e28f7a15e7f33ae1283e12fcaeec20d\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] D:\SDK\.gradle\caches\8.14\transforms\7e28f7a15e7f33ae1283e12fcaeec20d\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\0f7de0ab20d2d4aa3f16d22ed4d51da9\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\0f7de0ab20d2d4aa3f16d22ed4d51da9\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\d5ca53df2f4de933a04106af8059f679\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\d5ca53df2f4de933a04106af8059f679\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\53f51a02c7ff37d81e21ef5fdaeeb7a9\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\53f51a02c7ff37d81e21ef5fdaeeb7a9\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] D:\SDK\.gradle\caches\8.14\transforms\78314523cef4db59a8212cb4684bc3df\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] D:\SDK\.gradle\caches\8.14\transforms\78314523cef4db59a8212cb4684bc3df\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\eda307ef0a619080f1b352269a59a063\transformed\media3-datasource-okhttp-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\eda307ef0a619080f1b352269a59a063\transformed\media3-datasource-okhttp-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\2dd813087725868c007175ace6408115\transformed\media3-extractor-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\2dd813087725868c007175ace6408115\transformed\media3-extractor-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\c3fe6f3cfd0e972d23acbbfc417ab96c\transformed\media3-container-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\c3fe6f3cfd0e972d23acbbfc417ab96c\transformed\media3-container-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\a4146dbf6ba7b1142a57db9d1c131377\transformed\media3-datasource-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\a4146dbf6ba7b1142a57db9d1c131377\transformed\media3-datasource-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\bb2d7ea3a24fa745dfa140d5be189e97\transformed\media3-decoder-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\bb2d7ea3a24fa745dfa140d5be189e97\transformed\media3-decoder-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\bf810b01cfcb416f852f04018d32ab57\transformed\media3-database-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\bf810b01cfcb416f852f04018d32ab57\transformed\media3-database-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\ce241f4f00408d6d982f97b8bf43d28b\transformed\media3-common-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\ce241f4f00408d6d982f97b8bf43d28b\transformed\media3-common-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\5ba4a6657edbb78623de310dd1eca8ca\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.7.1] D:\SDK\.gradle\caches\8.14\transforms\5ba4a6657edbb78623de310dd1eca8ca\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] D:\SDK\.gradle\caches\8.14\transforms\399559d37d4f9952e3583715be17e0f6\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] D:\SDK\.gradle\caches\8.14\transforms\399559d37d4f9952e3583715be17e0f6\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\a418d9ee54fd83df91c25a130e4076d7\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\a418d9ee54fd83df91c25a130e4076d7\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\554a5d72ea9cae8c2cb92b51029dbc26\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\554a5d72ea9cae8c2cb92b51029dbc26\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\206c8a9644fba236f1db4793767b7fef\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\206c8a9644fba236f1db4793767b7fef\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\d44447578f55ada513db334036579163\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\d44447578f55ada513db334036579163\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\f9d7a7c2b70351e51375561b594e5917\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\f9d7a7c2b70351e51375561b594e5917\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\1c011daab12685aa09203682acc8e0f9\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\1c011daab12685aa09203682acc8e0f9\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\04dbf29eb04df97d8c6c0277c6c15c7d\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\04dbf29eb04df97d8c6c0277c6c15c7d\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\9e520c6756c32abb810b710b15b2c662\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\9e520c6756c32abb810b710b15b2c662\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\f16851d60c41318d8943e458bbeca9a2\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\f16851d60c41318d8943e458bbeca9a2\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\63f854a8edbd25820eac56875abaf822\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\63f854a8edbd25820eac56875abaf822\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\c70752de305f8b96513a22db66f735b0\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\c70752de305f8b96513a22db66f735b0\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\55a96b744d333b0991aced6d85bce14b\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\55a96b744d333b0991aced6d85bce14b\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\b491bcf5dd68b56d0a2b4a50410efb62\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\b491bcf5dd68b56d0a2b4a50410efb62\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\SDK\.gradle\caches\8.14\transforms\d91307c135b9d7e3689c4d1fe553fd42\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\SDK\.gradle\caches\8.14\transforms\d91307c135b9d7e3689c4d1fe553fd42\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\SDK\.gradle\caches\8.14\transforms\a442b737241294134111261aa83b3389\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\SDK\.gradle\caches\8.14\transforms\a442b737241294134111261aa83b3389\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\SDK\.gradle\caches\8.14\transforms\bd37db9f7ef03323382ae9af04943002\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\SDK\.gradle\caches\8.14\transforms\bd37db9f7ef03323382ae9af04943002\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\51b41db2807dde6044ff5d0449c971f3\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\51b41db2807dde6044ff5d0449c971f3\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\edb26f726f53caecf9f8a473a343f5c0\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\edb26f726f53caecf9f8a473a343f5c0\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\db2e86af43ceb3b81be2ee6d48d402ff\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\db2e86af43ceb3b81be2ee6d48d402ff\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\7ee722d410a53133d25228fe9f9df233\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\7ee722d410a53133d25228fe9f9df233\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\5ed9fe968b01500d2464018fae24ae8e\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\5ed9fe968b01500d2464018fae24ae8e\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\ede2c9572e5ad6a65b316ccee8fd469e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\ede2c9572e5ad6a65b316ccee8fd469e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\e7d1e06eb01e8974c785ff6974f0437a\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\e7d1e06eb01e8974c785ff6974f0437a\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\9c5d8cd33e427e7de704ba15bc178278\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\9c5d8cd33e427e7de704ba15bc178278\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\92ac6a56d7390863d6b4f2beccaba29a\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\92ac6a56d7390863d6b4f2beccaba29a\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\f7b7b12584c2fe6ad14ffff97c524bca\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\f7b7b12584c2fe6ad14ffff97c524bca\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\16e3f2687906432544aa0043af7a5bc5\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\16e3f2687906432544aa0043af7a5bc5\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\SDK\.gradle\caches\8.14\transforms\f26c8b0221aa77307cc910cc94957aff\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\SDK\.gradle\caches\8.14\transforms\f26c8b0221aa77307cc910cc94957aff\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] D:\SDK\.gradle\caches\8.14\transforms\dfbdb30de0961759c1a4169be9c68cad\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] D:\SDK\.gradle\caches\8.14\transforms\dfbdb30de0961759c1a4169be9c68cad\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\SDK\.gradle\caches\8.14\transforms\35e7c7b6047155d20b6dcb713663c4cd\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\SDK\.gradle\caches\8.14\transforms\35e7c7b6047155d20b6dcb713663c4cd\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\935e41b50c677756c00da7e6f2df7c22\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\935e41b50c677756c00da7e6f2df7c22\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\d7a49cf5bcfa8acdf4c990d2ac96b764\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\d7a49cf5bcfa8acdf4c990d2ac96b764\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\fd110c8bfe9179bf0260f30f0788b317\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\fd110c8bfe9179bf0260f30f0788b317\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\901ede9133fe3d4372b4955a87daedf9\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\901ede9133fe3d4372b4955a87daedf9\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\473191ebce6706d9bb70d3b6353c5b28\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\473191ebce6706d9bb70d3b6353c5b28\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\f55466f566e53e0e2bbdf1bb547ffaca\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\f55466f566e53e0e2bbdf1bb547ffaca\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] D:\SDK\.gradle\caches\8.14\transforms\22d66dc3e4353d95ada7e63cb6ce08d2\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] D:\SDK\.gradle\caches\8.14\transforms\22d66dc3e4353d95ada7e63cb6ce08d2\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\f39683438b1626831bb233ed2cba9a31\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\f39683438b1626831bb233ed2cba9a31\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] D:\SDK\.gradle\caches\8.14\transforms\e86fad15546fb9302ac2d731fb44fc17\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] D:\SDK\.gradle\caches\8.14\transforms\e86fad15546fb9302ac2d731fb44fc17\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] D:\SDK\.gradle\caches\8.14\transforms\da43022322a5a860f421f349ce046cac\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] D:\SDK\.gradle\caches\8.14\transforms\da43022322a5a860f421f349ce046cac\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\9a6eec2b4bc1b0987c0d4609e6984d48\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\9a6eec2b4bc1b0987c0d4609e6984d48\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\01b9ee1fe1ea2e0008327a7aa7d8eead\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\01b9ee1fe1ea2e0008327a7aa7d8eead\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.7] D:\SDK\.gradle\caches\8.14\transforms\09ff9ef8b1bf55042db9fb3aaf5c562f\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.7] D:\SDK\.gradle\caches\8.14\transforms\09ff9ef8b1bf55042db9fb3aaf5c562f\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] D:\SDK\.gradle\caches\8.14\transforms\d2fd20bbf8ba36ec95faa48a7be0bdc9\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] D:\SDK\.gradle\caches\8.14\transforms\d2fd20bbf8ba36ec95faa48a7be0bdc9\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.7] D:\SDK\.gradle\caches\8.14\transforms\12c9267fdaa49f9e9f1e9c661d516b85\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.7] D:\SDK\.gradle\caches\8.14\transforms\12c9267fdaa49f9e9f1e9c661d516b85\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] D:\SDK\.gradle\caches\8.14\transforms\838879e34554298736fd84f6b87f1be2\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] D:\SDK\.gradle\caches\8.14\transforms\838879e34554298736fd84f6b87f1be2\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\41a2cda2f46b7cd00f8f1f6af5d99772\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\41a2cda2f46b7cd00f8f1f6af5d99772\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\e1735ce06065d6ef67994e400b57f168\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] D:\SDK\.gradle\caches\8.14\transforms\e1735ce06065d6ef67994e400b57f168\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\SDK\.gradle\caches\8.14\transforms\efb014f164a5d3da771a81ddfa7c65cf\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\SDK\.gradle\caches\8.14\transforms\efb014f164a5d3da771a81ddfa7c65cf\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\87425f190765f41654b5b587646f2adb\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\87425f190765f41654b5b587646f2adb\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.14\transforms\ad613a999c5c8ee3883bbe6bfcfa78ca\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.14\transforms\ad613a999c5c8ee3883bbe6bfcfa78ca\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.2.0] D:\SDK\.gradle\caches\8.14\transforms\4d4a17d93d8d3c41e97371df5a6ce4d5\transformed\startup-runtime-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.2.0] D:\SDK\.gradle\caches\8.14\transforms\4d4a17d93d8d3c41e97371df5a6ce4d5\transformed\startup-runtime-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\SDK\.gradle\caches\8.14\transforms\f6976502fc9c64795fa15ec6ab5346f9\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\SDK\.gradle\caches\8.14\transforms\f6976502fc9c64795fa15ec6ab5346f9\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.2.0] D:\SDK\.gradle\caches\8.14\transforms\cf91559e9d7e2242136b065e3be61cd1\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] D:\SDK\.gradle\caches\8.14\transforms\cf91559e9d7e2242136b065e3be61cd1\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\3a4d47ac81379c954353de23ae92efb5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\3a4d47ac81379c954353de23ae92efb5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\791bac657cca89c17f064ee8c56eb28b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\SDK\.gradle\caches\8.14\transforms\791bac657cca89c17f064ee8c56eb28b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\SDK\.gradle\caches\8.14\transforms\93b3554daae3d7b3a889c209eb1bb130\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\SDK\.gradle\caches\8.14\transforms\93b3554daae3d7b3a889c209eb1bb130\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\tikoppij5\app\src\main\AndroidManifest.xml
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\5ed9fe968b01500d2464018fae24ae8e\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\5ed9fe968b01500d2464018fae24ae8e\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\5ed9fe968b01500d2464018fae24ae8e\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.tikoppij4.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.tikoppij4.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.14\transforms\905388a4024e2a0820709c33f70ff02b\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\ede2c9572e5ad6a65b316ccee8fd469e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\ede2c9572e5ad6a65b316ccee8fd469e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.14\transforms\ede2c9572e5ad6a65b316ccee8fd469e\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.14\transforms\90e806c0d39a5487f2ddc630a107549a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
