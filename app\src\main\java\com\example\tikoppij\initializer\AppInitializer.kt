package com.example.tikoppij.initializer

import android.content.Context
import androidx.startup.Initializer
import com.example.tikoppij.network.NetworkProvider
import com.example.tikoppij.utils.PerformanceMonitor
import com.example.tikoppij.video.MediaPlayerService
import org.koin.core.context.GlobalContext

/**
 * 应用统一初始化器
 * 整合性能监控、网络组件和视频播放服务的初始化
 * 使用 Koin 依赖注入框架管理所有组件
 */
class AppInitializer : Initializer<Unit> {
    
    companion object {
        /**
         * 获取媒体播放服务单例
         * 从 Koin 容器中获取实例
         */
        fun getMediaPlayerService(context: Context): MediaPlayerService {
            return GlobalContext.get().get<MediaPlayerService>()
        }
    }
    
    override fun create(context: Context) {
        // 1. 初始化性能监控系统
        PerformanceMonitor.initialize()
        PerformanceMonitor.recordTimePoint("性能监控初始化完成")
        
        // 2. 初始化网络组件
        PerformanceMonitor.recordTimePoint("网络组件初始化开始")
        NetworkProvider.apiHttpClient
        PerformanceMonitor.recordTimePoint("网络组件初始化完成")
        
        // 3. 媒体播放服务由 Koin 管理，无需手动初始化
        PerformanceMonitor.recordTimePoint("App Startup 初始化完成")
    }

    override fun dependencies(): List<Class<out Initializer<*>>> {
        // 无依赖项，这是基础组件
        return emptyList()
    }
} 