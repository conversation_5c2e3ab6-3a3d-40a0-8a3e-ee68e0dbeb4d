package com.example.tikoppij.di

import androidx.media3.common.util.UnstableApi
import com.example.tikoppij.data.DownloadRepository
import com.example.tikoppij.data.DownloadRepositoryImpl
import com.example.tikoppij.data.FavoriteRepository
import com.example.tikoppij.data.FavoriteRepositoryImpl
import com.example.tikoppij.data.HistoryRepository
import com.example.tikoppij.data.HistoryRepositoryImpl
import com.example.tikoppij.data.UserPreferencesRepository
import com.example.tikoppij.data.UserPreferencesRepositoryImpl
import com.example.tikoppij.video.MediaPlayerService
import com.example.tikoppij.viewmodel.KoinExampleViewModel
import org.koin.android.ext.koin.androidContext
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.dsl.module

/**
 * Koin 依赖注入模块
 * 定义应用中所有需要注入的依赖关系
 */
@UnstableApi
val appModule = module {
    
    // 数据仓库层 - 单例模式
    single<UserPreferencesRepository> { 
        UserPreferencesRepositoryImpl.getInstance(androidContext()) 
    }
    
    single<FavoriteRepository> { 
        FavoriteRepositoryImpl.getInstance(androidContext()) 
    }
    
    single<HistoryRepository> { 
        HistoryRepositoryImpl.getInstance(androidContext()) 
    }
    
    single<DownloadRepository> { 
        DownloadRepositoryImpl.getInstance(androidContext()) 
    }
    
    // 媒体播放服务 - 单例模式
    single<MediaPlayerService> { 
        MediaPlayerService(androidContext()) 
    }
}

/**
 * ViewModel 模块
 * 定义需要依赖注入的 ViewModel
 */
@UnstableApi
val viewModelModule = module {
    
    // 示例 ViewModel - 展示 Koin 依赖注入的使用
    viewModel { 
        KoinExampleViewModel(
            favoriteRepository = get(),
            historyRepository = get(),
            userPreferencesRepository = get(),
            mediaPlayerService = get()
        ) 
    }
}

/**
 * 所有模块的集合
 */
val allModules = listOf(appModule, viewModelModule) 