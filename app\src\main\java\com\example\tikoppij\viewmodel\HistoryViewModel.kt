package com.example.tikoppij.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.util.UnstableApi
import com.example.tikoppij.data.HistoryRepository
import com.example.tikoppij.di.getKoinInstance
import com.example.tikoppij.model.HistoryModel
import com.example.tikoppij.model.VideoModel
import com.example.tikoppij.utils.TimeUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * 历史记录列表ViewModel
 * 管理观看历史的显示和操作
 */
@UnstableApi
class HistoryViewModel(application: Application) : AndroidViewModel(application) {
    
    // 历史记录管理仓库 - 通过 Koin 获取
    private val historyRepository: HistoryRepository = getKoinInstance()
    
    // 历史记录列表
    private val _historyList = MutableStateFlow<List<HistoryModel>>(emptyList())
    val historyList: StateFlow<List<HistoryModel>> = _historyList.asStateFlow()
    
    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    init {
        loadHistoryList()
    }
    
    /**
     * 加载历史记录列表
     */
    private fun loadHistoryList() {
        viewModelScope.launch {
            _isLoading.value = true
            historyRepository.getHistoryList().collectLatest { history ->
                _historyList.value = history // 已经按观看时间倒序排列
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 从历史记录中移除视频
     */
    fun removeHistory(videoId: String) {
        viewModelScope.launch {
            historyRepository.removeHistory(videoId)
        }
    }
    
    /**
     * 清空历史记录
     */
    fun clearHistory() {
        viewModelScope.launch {
            historyRepository.clearHistory()
        }
    }
    
    /**
     * 获取当前历史视频列表作为VideoModel列表（用于播放）
     */
    fun getHistoryVideosAsVideoModels(): List<VideoModel> {
        return _historyList.value.map { it.toVideoModel() }
    }
    
    /**
     * 格式化观看时间
     */
    fun formatWatchTime(timestamp: Long): String = TimeUtils.formatRelativeTime(timestamp)
    
    /**
     * 格式化观看进度
     */
    fun formatWatchProgress(progress: Long): String = TimeUtils.formatPlaybackDuration(progress)
    
    override fun onCleared() {
        super.onCleared()
        // 清理资源（不依赖索引）
    }
} 