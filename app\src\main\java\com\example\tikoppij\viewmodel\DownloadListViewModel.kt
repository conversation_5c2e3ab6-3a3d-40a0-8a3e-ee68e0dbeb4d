package com.example.tikoppij.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.example.tikoppij.data.DownloadRepositoryImpl
import com.example.tikoppij.model.DownloadModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 下载列表ViewModel
 * 管理下载历史页面的数据
 */
class DownloadListViewModel(
    context: Context
) : ViewModel() {
    
    private val downloadRepository = DownloadRepositoryImpl.getInstance(context)
    
    // 下载列表
    private val _downloadList = MutableStateFlow<List<DownloadModel>>(emptyList())
    val downloadList: StateFlow<List<DownloadModel>> = _downloadList.asStateFlow()
    
    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    init {
        loadDownloads()
    }
    
    /**
     * 加载下载列表
     */
    private fun loadDownloads() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                downloadRepository.getDownloadList().collect { downloads ->
                    _downloadList.value = downloads
                    _isLoading.value = false
                }
            } catch (_: Exception) {
                _isLoading.value = false
                // 可以添加错误处理
            }
        }
    }
    
    /**
     * 删除单个下载记录
     */
    suspend fun removeDownload(videoId: String) {
        try {
            downloadRepository.removeDownload(videoId)
        } catch (_: Exception) {
            // 可以添加错误处理
        }
    }
    
    /**
     * 清空所有下载记录
     */
    suspend fun clearDownloads() {
        try {
            downloadRepository.clearDownloads()
        } catch (_: Exception) {
            // 可以添加错误处理
        }
    }
}

/**
 * DownloadListViewModel 工厂类
 */
class DownloadListViewModelFactory(
    private val context: Context
) : ViewModelProvider.Factory {
    
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(DownloadListViewModel::class.java)) {
            return DownloadListViewModel(context) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
} 