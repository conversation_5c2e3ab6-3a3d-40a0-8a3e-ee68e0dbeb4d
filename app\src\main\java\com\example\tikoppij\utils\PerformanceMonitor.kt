package com.example.tikoppij.utils

import android.util.Log

/**
 * 性能监控工具类
 * 用于记录和分析应用的性能数据，特别是启动时间
 */
object PerformanceMonitor {
    
    // 记录应用启动的开始时间
    private var appStartTime = 0L
    
    // 记录各阶段的时间点
    private val timePoints = mutableMapOf<String, Long>()
    
    /**
     * 初始化性能监控
     * 在应用启动时调用，支持App Startup
     */
    fun initialize() {
        appStartTime = System.currentTimeMillis()
        log("应用启动")
        recordTimePoint("应用启动")
    }
    
    /**
     * 记录时间点
     * @param name 时间点名称
     */
    fun recordTimePoint(name: String) {
        val currentTime = System.currentTimeMillis()
        timePoints[name] = currentTime
        
        // 计算与启动时间的差值
        val elapsedFromStart = currentTime - appStartTime
        log("$name: ${elapsedFromStart}ms")
    }

    /**
     * 记录日志
     * @param message 日志消息
     */
    private fun log(message: String) {
        if (Constants.Performance.ENABLE_LOGGING) {
            Log.d(Constants.Performance.MONITOR_TAG, message)
        }
    }
} 