package com.example.tikoppij.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.activity.compose.rememberLauncherForActivityResult

/**
 * 权限管理工具类
 * 处理存储权限的动态请求和检查
 */
object PermissionUtils {
    
    /**
     * 检查是否有下载到DCIM目录的权限
     * Android 10+ 不需要权限
     * Android 9及以下需要WRITE_EXTERNAL_STORAGE权限
     */
    fun hasDownloadPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ 可以直接写入DCIM目录，无需权限
            true
        } else {
            // Android 9及以下需要写入存储权限
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 获取需要请求的权限列表
     * 只在Android 9及以下需要
     */
    fun getRequiredPermissions(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ 无需权限
            emptyArray()
        } else {
            // Android 9及以下需要写入权限
            arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
    }
}

/**
 * Compose组件：权限请求处理器
 * 用于在UI中处理权限请求逻辑
 */
@Composable
fun PermissionHandler(
    onPermissionGranted: () -> Unit,
    onPermissionDenied: () -> Unit,
    onRequestPermission: (requestPermission: () -> Unit) -> Unit
) {
    val context = LocalContext.current
    val hasPermission = remember { mutableStateOf(PermissionUtils.hasDownloadPermission(context)) }
    
    val permissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        hasPermission.value = allGranted
        
        if (allGranted) {
            onPermissionGranted()
        } else {
            onPermissionDenied()
        }
    }
    
    // 将权限请求函数传递给调用者
    onRequestPermission {
        if (hasPermission.value) {
            onPermissionGranted()
        } else {
            val requiredPermissions = PermissionUtils.getRequiredPermissions()
            if (requiredPermissions.isNotEmpty()) {
                permissionLauncher.launch(requiredPermissions)
            } else {
                // Android 10+ 无需权限，直接授权
                onPermissionGranted()
            }
        }
    }
} 