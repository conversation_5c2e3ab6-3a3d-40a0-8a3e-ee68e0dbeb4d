# Tikoppij 项目架构文档

## 项目概述

Tikoppij 是一个基于 Android Jetpack Compose 开发的短视频播放应用，采用现代化的 Android 开发架构和技术栈。应用支持垂直滑动浏览视频、视频缓存、收藏管理、历史记录、下载功能等核心特性。

### 技术栈
- **UI框架**: Jetpack Compose
- **架构模式**: MVVM + Repository Pattern
- **视频播放**: ExoPlayer (Media3)
- **网络请求**: Retrofit + OkHttp
- **数据存储**: DataStore Preferences
- **依赖注入**: 手动依赖注入
- **异步处理**: Kotlin Coroutines + Flow
- **导航**: Navigation Compose

## 项目架构设计

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        UI Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Screens       │  │   Components    │  │  Navigation  │ │
│  │  - VideoScreen  │  │ - VideoPlayer   │  │ - AppNavGraph│ │
│  │  - ProfileScreen│  │ - BottomNavBar  │  │ - Destinations│ │
│  │  - ListScreens  │  │ - ControlButtons│  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    ViewModel Layer                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  VideoViewModel │  │ FavoriteViewModel│  │HistoryViewModel│ │
│  │ BaseVideoViewModel│ │CacheManagementVM│  │DownloadListVM│ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   Repository Layer                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │UserPreferencesRepo│ │ FavoriteRepo   │  │ HistoryRepo  │ │
│  │ DownloadRepo    │  │                │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     Service Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │MediaPlayerService│ │  NetworkProvider│  │ CacheManager │ │
│  │PlayerPoolManager│  │                │  │CustomDownload│ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      Data Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Data Models   │  │   DataStore     │  │  Network API │ │
│  │ - VideoModel    │  │ - Preferences   │  │ - ApiService │ │
│  │ - FavoriteModel │  │ - Cache Data    │  │ - Retrofit   │ │
│  │ - HistoryModel  │  │                │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 模块详细说明

### 1. 应用入口模块

#### 1.1 MyApplication.kt
**功能**: 应用程序主类，负责全局初始化
**核心特性**:
- 全局单例仓库管理（用户偏好、收藏、历史）
- 性能监控集成
- 最小化初始化工作，利用 App Startup 优化启动性能

```kotlin
// 全局访问的关键服务实例
companion object {
    lateinit var userPreferencesRepository: UserPreferencesRepository
    lateinit var favoriteRepository: FavoriteRepository  
    lateinit var historyRepository: HistoryRepository
}
```

#### 1.2 MainActivity.kt
**功能**: 主Activity，管理应用的整体UI结构
**核心特性**:
- Edge-to-Edge 沉浸式体验
- 动态状态栏控制（根据页面类型调整图标颜色）
- 底部导航栏动态显示/隐藏
- 抽屉导航集成
- 双击退出应用逻辑
- 屏幕常亮控制

#### 1.3 AppInitializer.kt
**功能**: 统一初始化器，使用 App Startup 优化启动性能
**核心特性**:
- 性能监控系统初始化
- 网络组件预加载
- 媒体播放服务单例管理
- 并行初始化，减少启动时间

### 2. UI层模块

#### 2.1 导航系统 (ui/navigation/)

**AppNavigation.kt**
- 定义应用所有路由和导航目标
- 支持无动画切换，优化性能
- 播放器页面数据传递机制
- 底部导航栏配置

```kotlin
// 主要路由定义
sealed class AppDestinations(val route: String) {
    data object Home : AppDestinations("home")
    data object Tools : AppDestinations("tools") 
    data object Hidden : AppDestinations("hidden")
    data object Profile : AppDestinations("profile")
    // ... 其他路由
}
```

#### 2.2 主要界面 (ui/screens/)

**VideoScreen.kt - 首页视频界面**
- 垂直滑动的视频播放器（VerticalPager）
- 智能播放状态管理
- 应用生命周期感知播放控制
- 底部菜单集成
- 控制按钮动态显示逻辑

**ProfileScreen.kt - 个人中心**
- 用户信息展示
- 功能菜单导航（收藏、历史、下载、缓存管理）
- 卡片式布局设计

**FavoriteListScreen.kt - 收藏列表**
- 收藏视频列表展示
- 批量清空功能
- 空状态处理
- 跳转播放器功能

**HistoryListScreen.kt - 历史记录**
- 观看历史展示
- 观看进度显示
- 时间格式化显示
- 历史记录管理

**DownloadListScreen.kt - 下载管理**
- 下载文件列表
- 下载统计信息
- 文件删除管理
- 本地播放支持

#### 2.3 核心组件 (ui/components/)

**VideoPlayerComponent.kt - 视频播放器组件**
- 基于 TextureView 的视频渲染
- 多种显示模式（自动适应/完整显示）
- 手势控制（点击播放/暂停、长按菜单）
- 进度条拖拽控制
- 播放状态同步机制

```kotlin
enum class VideoDisplayMode {
    AUTO_ADAPT, // 自动适应：横屏完整显示，竖屏填充
    FIT        // 适应模式：所有视频都完整显示
}
```

**VideoControlButtons.kt - 视频控制按钮**
- 收藏/取消收藏
- 下载功能
- 分享功能
- 菜单显示
- 导航栏切换

**BottomNavBar.kt - 底部导航栏**
- 四个主要导航项
- 选中状态指示
- 图标和文字显示

**NavigationDrawerComponent.kt - 抽屉导航**
- 侧滑菜单实现
- 手势控制
- 内容区域自定义

#### 2.4 主题系统 (ui/theme/)

**Theme.kt**
- Material 3 主题配置
- 动态颜色支持（Android 12+）
- 深色/浅色主题切换

**Color.kt**
- 应用色彩定义
- 视频播放器专用颜色
- 导航栏颜色配置

### 3. ViewModel层

#### 3.1 BaseVideoViewModel.kt
**功能**: 视频相关ViewModel的基类
**核心特性**:
- 媒体播放服务集成
- 用户偏好设置管理
- 收藏功能通用逻辑
- 历史记录管理

#### 3.2 VideoViewModel.kt
**功能**: 首页视频播放的核心业务逻辑
**核心特性**:
- 视频列表数据管理
- 分批加载优化（首屏3个，后续10个）
- 播放索引管理
- 自动播放下一个视频
- 视频结束处理逻辑

```kotlin
// 加载策略
private fun loadInitialVideos() {
    // 首屏快速加载3个视频
    val initialVideos = loadVideos(count = 3)
    // 后台继续加载更多
    loadVideos(skip = 3, count = 10)
}
```

#### 3.3 其他ViewModel
- **FavoriteViewModel**: 收藏管理
- **HistoryViewModel**: 历史记录管理  
- **DownloadListViewModel**: 下载管理
- **CacheManagementViewModel**: 缓存管理

### 4. 数据层

#### 4.1 数据模型 (model/)

**VideoModel.kt**
```kotlin
data class VideoModel(
    val url: String,         // 视频播放链接
    val video_id: String,    // 视频ID
    val Category: Int,       // 分类
    val width: Int,          // 视频宽度
    val height: Int,         // 视频高度
) {
    fun getAspectRatio(): Float = 
        if (height != 0) width.toFloat() / height.toFloat() else 16f / 9f
}
```

**FavoriteModel.kt & HistoryModel.kt**
- 收藏和历史数据结构
- 时间戳记录
- 转换为VideoModel的方法

#### 4.2 数据仓库 (data/)

**UserPreferencesRepository.kt**
- 基于 DataStore 的用户偏好存储
- 视频显示模式设置
- 自动播放下一个设置
- 缓存大小配置

**FavoriteRepository.kt & HistoryRepository.kt**
- 收藏和历史数据的CRUD操作
- 数据持久化
- 数量限制管理

#### 4.3 网络层 (network/)

**NetworkProvider.kt**
- Retrofit + OkHttp 网络配置
- 分离的API客户端和视频客户端
- 超时时间优化配置
- 日志拦截器

```kotlin
interface ApiService {
    @GET("v1/api/url")
    suspend fun getVideoList(
        @Query("c") category: Int = 99,
        @Query("r") count: Int = 10
    ): Response<VideoResponse>
}
```

### 5. 核心服务层

#### 5.1 MediaPlayerService.kt
**功能**: 媒体播放服务的统一协调层
**核心特性**:
- 整合 CacheManager 和 PlayerPoolManager
- 提供统一的播放控制接口
- 缓存管理功能代理
- 播放状态管理

#### 5.2 PlayerPoolManager.kt
**功能**: ExoPlayer 实例池管理器
**核心特性**:
- 固定3个播放器槽位，优化内存使用
- 智能播放器复用逻辑
- 播放状态持久化
- 硬件加速优化配置

```kotlin
// 播放器创建优化
private fun createPlayer(video: VideoModel): ExoPlayer {
    val loadControl = DefaultLoadControl.Builder()
        .setBufferDurationsMs(2000, 50000, 1000, 2000)
        .build()
    
    val renderersFactory = DefaultRenderersFactory(context)
        .experimentalSetEnableMediaCodecVideoRendererPrewarming(true)
    
    return ExoPlayer.Builder(context)
        .setRenderersFactory(renderersFactory)
        .setLoadControl(loadControl)
        .build()
}
```

#### 5.3 CacheManager.kt
**功能**: 视频缓存管理器
**核心特性**:
- ExoPlayer SimpleCache 集成
- 动态缓存大小调整
- LRU 缓存策略
- 缓存状态检查
- 用户偏好集成

#### 5.4 CustomDownloadManager.kt
**功能**: 自定义下载管理器
**核心特性**:
- 不依赖系统DownloadManager
- 实时下载进度回调
- MediaStore API 集成（Android 10+）
- 文件保存到 DCIM/tikapp 目录
- 下载状态管理

### 6. 工具类模块 (utils/)

#### 6.1 Constants.kt
**功能**: 应用常量统一管理
**配置项**:
- 视频加载策略配置
- 缓存大小限制
- 网络超时配置
- 性能监控开关

#### 6.2 PerformanceMonitor.kt
**功能**: 性能监控工具
**特性**:
- 关键时间点记录
- 启动性能分析
- 可配置的日志输出

#### 6.3 其他工具类
- **PermissionUtils.kt**: 权限管理
- **ShareUtils.kt**: 分享功能
- **FileUtils.kt**: 文件操作
- **LifecycleManager.kt**: 生命周期管理

## 技术特点和亮点

### 1. 性能优化
- **启动优化**: App Startup 并行初始化
- **内存优化**: 播放器池复用，固定槽位设计
- **加载优化**: 分批加载策略，首屏快速响应
- **缓存优化**: 智能缓存策略，LRU算法

### 2. 用户体验
- **沉浸式体验**: Edge-to-Edge + 动态状态栏
- **流畅播放**: 硬件加速 + 预缓冲
- **智能控制**: 手势操作 + 自动播放
- **状态保持**: 播放进度记录 + 断点续播

### 3. 架构设计
- **模块化**: 清晰的分层架构
- **可扩展**: Repository 模式 + 依赖注入
- **可维护**: 统一的错误处理和日志
- **可测试**: ViewModel + Repository 分离

### 4. 数据管理
- **持久化**: DataStore 替代 SharedPreferences
- **状态管理**: StateFlow + Compose 响应式UI
- **缓存策略**: 多级缓存 + 智能清理
- **离线支持**: 本地下载 + 离线播放

## 项目配置

### 依赖管理
```kotlin
dependencies {
    // Compose UI
    implementation(libs.androidx.compose.bom)
    implementation(libs.androidx.ui)
    implementation(libs.androidx.material3)
    
    // Media3 (ExoPlayer)
    implementation(libs.androidx.media3.exoplayer)
    implementation(libs.androidx.media3.datasource.okhttp)
    
    // 网络
    implementation(libs.retrofit)
    implementation(libs.okhttp)
    
    // 协程
    implementation(libs.kotlinx.coroutines.android)
    
    // DataStore
    implementation(libs.androidx.datastore.preferences)
    
    // App Startup
    implementation(libs.androidx.startup.runtime)
}
```

### 权限配置
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
    android:maxSdkVersion="28" />
```

## 总结

Tikoppij 项目采用了现代化的 Android 开发最佳实践，具有以下特点：

1. **技术先进**: 全面使用 Jetpack Compose + Media3 + Kotlin Coroutines
2. **架构清晰**: MVVM + Repository 模式，职责分离明确
3. **性能优秀**: 多项性能优化措施，启动快速，播放流畅
4. **用户友好**: 沉浸式体验，智能交互，功能完善
5. **代码质量**: 模块化设计，可维护性强，扩展性好

该项目展现了一个成熟的短视频应用应有的技术水准和用户体验，是 Android 现代化开发的优秀实践案例。 