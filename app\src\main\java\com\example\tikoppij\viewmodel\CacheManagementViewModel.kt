package com.example.tikoppij.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.util.UnstableApi
import com.example.tikoppij.data.UserPreferencesRepository
import com.example.tikoppij.di.getKoinInstance
import com.example.tikoppij.utils.Constants
import com.example.tikoppij.utils.FileUtils
import com.example.tikoppij.video.MediaPlayerService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * 缓存管理ViewModel
 * 负责缓存设置和状态管理
 */
@UnstableApi
class CacheManagementViewModel(application: Application) : AndroidViewModel(application) {
    
    // 媒体播放服务 - 通过 Koin 获取
    private val mediaPlayerService: MediaPlayerService = getKoinInstance()
    
    // 用户偏好设置仓库 - 通过 Koin 获取
    private val userPreferencesRepository: UserPreferencesRepository = getKoinInstance()
    
    // 当前缓存大小（字节）
    private val _currentCacheSize = MutableStateFlow(0L)

    // 当前缓存大小（格式化）
    private val _currentCacheSizeFormatted = MutableStateFlow("")
    val currentCacheSizeFormatted: StateFlow<String> = _currentCacheSizeFormatted.asStateFlow()
    
    // 最大缓存大小（字节）
    private val _maxCacheSize = MutableStateFlow(Constants.Cache.MIN_CACHE_SIZE)
    val maxCacheSize: StateFlow<Long> = _maxCacheSize.asStateFlow()
    
    // 最大缓存大小（格式化）
    private val _maxCacheSizeFormatted = MutableStateFlow("")
    val maxCacheSizeFormatted: StateFlow<String> = _maxCacheSizeFormatted.asStateFlow()
    
    // 缓存清理状态
    private val _isClearingCache = MutableStateFlow(false)
    val isClearingCache: StateFlow<Boolean> = _isClearingCache.asStateFlow()
    
    // 缓存使用百分比
    private val _cacheUsagePercent = MutableStateFlow(0f)
    val cacheUsagePercent: StateFlow<Float> = _cacheUsagePercent.asStateFlow()
    
    // 清理成功提示
    private val _clearSuccessMessage = MutableStateFlow<String?>(null)
    val clearSuccessMessage: StateFlow<String?> = _clearSuccessMessage.asStateFlow()
    
    // 可选的缓存大小选项（MB）
    val cacheSizeOptions = listOf(
        500,       // 500MB
        1024,      // 1GB
        2048,      // 2GB
        3072,      // 3GB
        4096       // 4GB
    )
    
    init {
        // 加载初始数据
        refreshCacheInfo()
        
        // 监听用户偏好中的最大缓存大小设置
        viewModelScope.launch {
            userPreferencesRepository.maxCacheSize.collectLatest { size ->
                _maxCacheSize.value = size
                _maxCacheSizeFormatted.value = FileUtils.formatFileSize(size)
                updateCacheUsagePercent()
            }
        }
    }
    
    /**
     * 刷新缓存信息
     */
    fun refreshCacheInfo() {
        viewModelScope.launch {
            val currentSize = mediaPlayerService.getCurrentCacheSize()
            _currentCacheSize.value = currentSize
            _currentCacheSizeFormatted.value = mediaPlayerService.getCurrentCacheSizeFormatted()
            
            val maxSize = mediaPlayerService.getMaxCacheSize()
            _maxCacheSize.value = maxSize
            _maxCacheSizeFormatted.value = mediaPlayerService.getMaxCacheSizeFormatted()
            
            updateCacheUsagePercent()
        }
    }
    
    /**
     * 更新缓存使用百分比
     */
    private fun updateCacheUsagePercent() {
        val current = _currentCacheSize.value
        val max = _maxCacheSize.value
        
        _cacheUsagePercent.value = if (max > 0) {
            (current.toFloat() / max.toFloat()).coerceIn(0f, 1f)
        } else {
            0f
        }
    }
    
    /**
     * 设置最大缓存大小
     * @param sizeMB 缓存大小（MB）
     */
    fun setMaxCacheSize(sizeMB: Int) {
        val sizeBytes = sizeMB * Constants.FileSize.BYTES_PER_MB
        
        viewModelScope.launch {
            try {
                mediaPlayerService.setMaxCacheSize(sizeBytes)
                refreshCacheInfo()
            } catch (_: Exception) {
                // 错误处理
            }
        }
    }
    
    /**
     * 清理所有缓存
     * 简化逻辑：直接清理并刷新状态
     */
    fun clearAllCache() {
        viewModelScope.launch {
            _isClearingCache.value = true
            
            try {
                // 执行清理操作
                val success = mediaPlayerService.clearAllCache()
                
                // 清理完成后刷新状态
                if (success) {
                    refreshCacheInfo()
                    _clearSuccessMessage.value = "缓存清理成功"
                } else {
                    _clearSuccessMessage.value = "缓存清理失败，请重试"
                }
            } catch (_: Exception) {
                // 出错时也刷新状态
                refreshCacheInfo()
                _clearSuccessMessage.value = "缓存清理异常，请重试"
            } finally {
                _isClearingCache.value = false
            }
        }
    }
    
    /**
     * 清除成功消息
     */
    fun clearSuccessMessage() {
        _clearSuccessMessage.value = null
    }
} 