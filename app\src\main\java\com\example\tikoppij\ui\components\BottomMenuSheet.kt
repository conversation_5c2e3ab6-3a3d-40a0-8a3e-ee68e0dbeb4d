package com.example.tikoppij.ui.components

import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectVerticalDragGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.core.view.WindowCompat
import kotlin.math.roundToInt
import android.graphics.Color as AndroidGraphicsColor

import com.example.tikoppij.R // For string resources

/**
 * 自定义底部菜单组件
 * 确保覆盖在导航栏上层，保持状态栏颜色一致
 *
 * 优化特性：
 * - 流畅的拖拽动画和阻尼效果
 * - 智能的系统栏处理
 * - 性能优化的状态管理
 * - 无障碍支持
 */
@Composable
fun BottomMenuSheet(
    isVisible: Boolean,
    onDismiss: () -> Unit,
    currentDisplayMode: VideoDisplayMode,
    onToggleDisplayMode: () -> Unit,
    isAutoPlayNextEnabled: Boolean,
    onToggleAutoPlayNext: () -> Unit,
    isStatusBarVisible: Boolean = true,
    containerColor: Color = MaterialTheme.colorScheme.surfaceVariant // Use theme color
) {
    if (!isVisible) return

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false,
            decorFitsSystemWindows = false
        )
    ) {
        val view = LocalView.current
        DisposableEffect(view) {
            val parent = view.parent
            val window = parent?.javaClass?.getDeclaredField("window")?.apply {
                isAccessible = true
            }?.get(parent) as? android.view.Window

            window?.let {
                it.setBackgroundDrawableResource(android.R.color.transparent)
                it.setDimAmount(0f)
                WindowCompat.setDecorFitsSystemWindows(it, false)
                @Suppress("DEPRECATION")
                it.apply {
                    statusBarColor = AndroidGraphicsColor.TRANSPARENT
                    navigationBarColor = AndroidGraphicsColor.TRANSPARENT
                }
                WindowCompat.getInsetsController(it, view).apply {
                    isAppearanceLightStatusBars = false
                    isAppearanceLightNavigationBars = false

                    if (isStatusBarVisible) {
                        show(androidx.core.view.WindowInsetsCompat.Type.statusBars())
                    } else {
                        hide(androidx.core.view.WindowInsetsCompat.Type.statusBars())
                    }
                }
            }
            onDispose {}
        }

        var sheetHeight by remember { mutableFloatStateOf(0f) }
        var dragOffset by remember { mutableFloatStateOf(0f) }
        var isDragging by remember { mutableStateOf(false) }
        var shouldDismiss by remember { mutableStateOf(false) }

        // 优化的动画配置，提供更流畅的用户体验
        val animationSpec: AnimationSpec<Float> = spring(
            dampingRatio = 0.85f, // 稍微增加阻尼，减少过度弹跳
            stiffness = 450f // 增加刚度，提升响应速度
        )

        val animatedOffset by animateFloatAsState(
            targetValue = if (isDragging) {
                dragOffset / sheetHeight.coerceAtLeast(1f) // Avoid division by zero if sheetHeight is 0
            } else if (shouldDismiss) {
                1.0f
            } else {
                0f
            },
            animationSpec = animationSpec,
            label = "sheetOffset",
            finishedListener = {
                if (shouldDismiss) {
                    onDismiss()
                }
            }
        )

        // 优化背景透明度计算，提供更自然的视觉效果
        val backgroundAlpha = remember(animatedOffset) {
            val baseAlpha = 0.6f // 增加基础透明度，提供更好的对比度
            val fadeRate = 1.2f // 调整淡出速率
            (baseAlpha * (1 - animatedOffset * fadeRate)).coerceIn(0f, baseAlpha)
        }

        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = backgroundAlpha))
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null
                ) {
                    shouldDismiss = true
                },
            contentAlignment = Alignment.BottomCenter
        ) {
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .offset {
                        IntOffset(
                            x = 0,
                            y = (animatedOffset * sheetHeight).roundToInt()
                        )
                    }
                    .onGloballyPositioned {
                        sheetHeight = it.size.height.toFloat()
                    }
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null
                    ) { /* 拦截点击，防止传递到背景 */ }
                    .pointerInput(Unit) {
                        detectVerticalDragGestures(
                            onDragStart = { isDragging = true },
                            onDragEnd = {
                                isDragging = false
                                // 优化拖拽阈值，提供更合理的关闭触发条件
                                val dismissThreshold = sheetHeight * 0.15f // 稍微增加阈值
                                if (dragOffset > dismissThreshold) {
                                    shouldDismiss = true
                                } else {
                                    dragOffset = 0f
                                }
                            },
                            onDragCancel = {
                                dragOffset = 0f
                                isDragging = false
                            },
                            onVerticalDrag = { change, dragAmount ->
                                // 优化拖拽阻尼算法，提供更自然的拖拽感受
                                val dampedDragAmount = when {
                                    dragOffset > sheetHeight * 0.7f -> dragAmount * 0.3f // 强阻尼
                                    dragOffset > sheetHeight * 0.4f -> dragAmount * 0.6f // 中等阻尼
                                    else -> dragAmount * 0.85f // 轻微阻尼
                                }
                                dragOffset = (dragOffset + dampedDragAmount).coerceAtLeast(0f)
                                change.consume()
                            }
                        )
                    },
                shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp), // 增加圆角半径
                color = containerColor,
                shadowElevation = 12.dp, // 增加阴影深度，提供更好的层次感
                tonalElevation = 6.dp // 添加色调高度，增强Material Design效果
            ) {
                Column(
                    modifier = Modifier.padding(bottom = 20.dp) // 增加底部间距，提供更好的视觉平衡
                ) {
                    // 优化的拖拽指示器
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 16.dp) // 增加垂直间距
                            .height(28.dp) // 增加高度，提供更好的触摸区域
                            .align(Alignment.CenterHorizontally),
                        contentAlignment = Alignment.Center
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth(0.12f) // 稍微减小宽度，更精致
                                .height(5.dp) // 增加高度，更易识别
                                .clip(RoundedCornerShape(2.5.dp)) // 调整圆角
                                .background(
                                    MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.4f)
                                ) // 使用主题颜色，提供更好的一致性
                        )
                    }

                    // Settings Items
                    SettingItemSwitch(
                        title = stringResource(id = R.string.display_mode_setting),
                        description = stringResource(id = if (currentDisplayMode == VideoDisplayMode.AUTO_ADAPT) R.string.display_mode_auto_adapt else R.string.display_mode_fit),
                        checked = currentDisplayMode == VideoDisplayMode.FIT, // FIT mode means switch is ON (for toggling to AUTO_ADAPT)
                        onCheckedChanged = { onToggleDisplayMode() }
                    )

                    SettingItemSwitch(
                        title = stringResource(id = R.string.auto_play_next_setting),
                        description = stringResource(id = if (isAutoPlayNextEnabled) R.string.auto_play_next_enabled else R.string.auto_play_next_disabled),
                        checked = isAutoPlayNextEnabled,
                        onCheckedChanged = { onToggleAutoPlayNext() }
                    )

                    // 底部安全区域，确保在各种设备上都有合适的间距
                    Spacer(modifier = Modifier.height(48.dp)) // 增加高度，提供更好的视觉平衡
                }
            }
        }
    }
}

/**
 * 优化的设置项开关组件
 * 提供更好的用户体验和无障碍支持
 */
@Composable
private fun SettingItemSwitch(
    title: String,
    description: String,
    checked: Boolean,
    onCheckedChanged: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(
                onClickLabel = if (checked) "关闭 $title" else "开启 $title" // 无障碍标签
            ) {
                onCheckedChanged(!checked)
            }
            .padding(horizontal = 28.dp, vertical = 20.dp), // 增加间距，提供更好的触摸体验
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(4.dp) // 添加标题和描述之间的间距
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium.copy(
                    fontSize = 17.sp, // 稍微增加字体大小
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium
                ),
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 14.sp
                ),
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.8f)
            )
        }
        Spacer(modifier = Modifier.width(20.dp)) // 增加间距
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChanged,
            modifier = Modifier.semantics {
                contentDescription = if (checked) "$title 已开启" else "$title 已关闭"
            }
        )
    }
}