com.example.tikoppij4:xml/data_extraction_rules = 0x7f110001
com.example.tikoppij4:styleable/View = 0x7f100034
com.example.tikoppij4:styleable/SearchView = 0x7f10002d
com.example.tikoppij4:styleable/NavGraphNavigator = 0x7f100026
com.example.tikoppij4:styleable/NavArgument = 0x7f100024
com.example.tikoppij4:styleable/NavAction = 0x7f100023
com.example.tikoppij4:styleable/MenuView = 0x7f100022
com.example.tikoppij4:styleable/MenuItem = 0x7f100021
com.example.tikoppij4:styleable/LinearLayoutCompat = 0x7f10001d
com.example.tikoppij4:styleable/CompoundButton = 0x7f100015
com.example.tikoppij4:styleable/ColorStateListItem = 0x7f100014
com.example.tikoppij4:styleable/ButtonBarLayout = 0x7f100011
com.example.tikoppij4:styleable/AppCompatSeekBar = 0x7f10000d
com.example.tikoppij4:styleable/AppCompatImageView = 0x7f10000c
com.example.tikoppij4:styleable/AppCompatEmojiHelper = 0x7f10000b
com.example.tikoppij4:styleable/AnimatedStateListDrawableTransition = 0x7f10000a
com.example.tikoppij4:styleable/AnimatedStateListDrawableCompat = 0x7f100008
com.example.tikoppij4:styleable/ActivityNavigator = 0x7f100006
com.example.tikoppij4:styleable/ActionMode = 0x7f100004
com.example.tikoppij4:style/Widget.Compat.NotificationActionText = 0x7f0f0162
com.example.tikoppij4:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f0f015e
com.example.tikoppij4:style/Widget.AppCompat.TextView = 0x7f0f015d
com.example.tikoppij4:style/Widget.AppCompat.RatingBar.Indicator = 0x7f0f0153
com.example.tikoppij4:style/Widget.AppCompat.PopupWindow = 0x7f0f014f
com.example.tikoppij4:style/Widget.AppCompat.Toolbar = 0x7f0f015f
com.example.tikoppij4:style/Widget.AppCompat.PopupMenu = 0x7f0f014d
com.example.tikoppij4:style/Widget.AppCompat.ListView.Menu = 0x7f0f014c
com.example.tikoppij4:style/Widget.AppCompat.ListPopupWindow = 0x7f0f0149
com.example.tikoppij4:style/Widget.AppCompat.Light.SearchView = 0x7f0f0146
com.example.tikoppij4:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0f0145
com.example.tikoppij4:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f0f0143
com.example.tikoppij4:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f0f0140
com.example.tikoppij4:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f0f013f
com.example.tikoppij4:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f0f013e
com.example.tikoppij4:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f0f013d
com.example.tikoppij4:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f0f013c
com.example.tikoppij4:style/Widget.AppCompat.Light.ActionButton = 0x7f0f013b
com.example.tikoppij4:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0f0138
com.example.tikoppij4:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f0f0137
com.example.tikoppij4:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f0f0136
com.example.tikoppij4:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0f0135
com.example.tikoppij4:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f0f0134
com.example.tikoppij4:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f0f0133
com.example.tikoppij4:style/Widget.AppCompat.CompoundButton.Switch = 0x7f0f012d
com.example.tikoppij4:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f0f012b
com.example.tikoppij4:style/Widget.AppCompat.Button.Small = 0x7f0f0128
com.example.tikoppij4:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0f0126
com.example.tikoppij4:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f0f0125
com.example.tikoppij4:style/Widget.AppCompat.Button.Borderless = 0x7f0f0124
com.example.tikoppij4:style/Widget.AppCompat.ActionMode = 0x7f0f0120
com.example.tikoppij4:style/Widget.AppCompat.ActionButton.Overflow = 0x7f0f011f
com.example.tikoppij4:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f0f011e
com.example.tikoppij4:style/Widget.AppCompat.ActionBar.TabText = 0x7f0f011b
com.example.tikoppij4:style/Widget.AppCompat.ActionBar.Solid = 0x7f0f0119
com.example.tikoppij4:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0f0116
com.example.tikoppij4:styleable/Capability = 0x7f100012
com.example.tikoppij4:style/ThemeOverlay.AppCompat.Dialog = 0x7f0f0115
com.example.tikoppij4:style/ThemeOverlay.AppCompat.DayNight = 0x7f0f0113
com.example.tikoppij4:style/ThemeOverlay.AppCompat.Dark = 0x7f0f0111
com.example.tikoppij4:style/ThemeOverlay.AppCompat.ActionBar = 0x7f0f0110
com.example.tikoppij4:style/ThemeOverlay.AppCompat = 0x7f0f010f
com.example.tikoppij4:style/Theme.AppCompat.Light.NoActionBar = 0x7f0f010d
com.example.tikoppij4:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f0f010a
com.example.tikoppij4:style/Theme.AppCompat.Light.DarkActionBar = 0x7f0f0108
com.example.tikoppij4:style/Theme.AppCompat.Light = 0x7f0f0107
com.example.tikoppij4:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f0f00ff
com.example.tikoppij4:style/Theme.AppCompat.DayNight = 0x7f0f00fb
com.example.tikoppij4:style/TextAppearance.Compat.Notification.Title = 0x7f0f00f5
com.example.tikoppij4:style/TextAppearance.Compat.Notification.Line2 = 0x7f0f00f3
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.Switch = 0x7f0f00ef
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0f00ed
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0f00eb
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0f00ea
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0f00e5
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0f00e2
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0f00df
com.example.tikoppij4:style/TextAppearance.AppCompat.Small.Inverse = 0x7f0f00d8
com.example.tikoppij4:style/TextAppearance.AppCompat.Small = 0x7f0f00d7
com.example.tikoppij4:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0f00d5
com.example.tikoppij4:style/Widget.AppCompat.Spinner.DropDown = 0x7f0f015a
com.example.tikoppij4:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f0f00d3
com.example.tikoppij4:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f0f0114
com.example.tikoppij4:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0f00d1
com.example.tikoppij4:style/Widget.AppCompat.Button.Colored = 0x7f0f0127
com.example.tikoppij4:style/TextAppearance.AppCompat.Headline = 0x7f0f00ca
com.example.tikoppij4:style/TextAppearance.AppCompat.Display4 = 0x7f0f00c9
com.example.tikoppij4:style/TextAppearance.AppCompat.Display3 = 0x7f0f00c8
com.example.tikoppij4:styleable/ActionBar = 0x7f100000
com.example.tikoppij4:style/TextAppearance.AppCompat.Caption = 0x7f0f00c5
com.example.tikoppij4:style/Widget.Compat.NotificationActionContainer = 0x7f0f0161
com.example.tikoppij4:style/TextAppearance.AppCompat.Body2 = 0x7f0f00c3
com.example.tikoppij4:style/Widget.AppCompat.Light.ActionBar = 0x7f0f0132
com.example.tikoppij4:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f0f00be
com.example.tikoppij4:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f0f00bc
com.example.tikoppij4:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f0f00ba
com.example.tikoppij4:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f0f00b9
com.example.tikoppij4:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f0f00b8
com.example.tikoppij4:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f0f00b7
com.example.tikoppij4:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f0f00b6
com.example.tikoppij4:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f0f00b5
com.example.tikoppij4:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f0f00b0
com.example.tikoppij4:styleable/AppCompatTheme = 0x7f100010
com.example.tikoppij4:style/Platform.Widget.AppCompat.Spinner = 0x7f0f00af
com.example.tikoppij4:style/Theme.AppCompat.DayNight.Dialog = 0x7f0f00fd
com.example.tikoppij4:style/Platform.V25.AppCompat = 0x7f0f00ad
com.example.tikoppij4:style/Platform.V21.AppCompat.Light = 0x7f0f00ac
com.example.tikoppij4:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f0f00a9
com.example.tikoppij4:style/FloatingDialogWindowTheme = 0x7f0f00a5
com.example.tikoppij4:style/EdgeToEdgeFloatingDialogWindowTheme = 0x7f0f00a3
com.example.tikoppij4:style/EdgeToEdgeFloatingDialogTheme = 0x7f0f00a2
com.example.tikoppij4:style/DialogWindowTheme = 0x7f0f00a1
com.example.tikoppij4:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f0f009c
com.example.tikoppij4:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f0f009a
com.example.tikoppij4:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f0f0098
com.example.tikoppij4:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f0f0096
com.example.tikoppij4:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f0f0095
com.example.tikoppij4:style/Base.Widget.AppCompat.RatingBar = 0x7f0f0094
com.example.tikoppij4:style/Base.Widget.AppCompat.ProgressBar = 0x7f0f0092
com.example.tikoppij4:style/Base.Widget.AppCompat.PopupWindow = 0x7f0f0091
com.example.tikoppij4:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f0f0090
com.example.tikoppij4:style/Base.Widget.AppCompat.ListView = 0x7f0f008c
com.example.tikoppij4:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f0f008b
com.example.tikoppij4:style/Base.Widget.AppCompat.ListMenuView = 0x7f0f008a
com.example.tikoppij4:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0f0089
com.example.tikoppij4:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f0f0088
com.example.tikoppij4:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f0f0087
com.example.tikoppij4:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f0f0085
com.example.tikoppij4:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0f0084
com.example.tikoppij4:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f0f0083
com.example.tikoppij4:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f0f0082
com.example.tikoppij4:style/Base.Widget.AppCompat.ImageButton = 0x7f0f0081
com.example.tikoppij4:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f0f007f
com.example.tikoppij4:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f0f007e
com.example.tikoppij4:style/TextAppearance.AppCompat.Display1 = 0x7f0f00c6
com.example.tikoppij4:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f0f007c
com.example.tikoppij4:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f0f007b
com.example.tikoppij4:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0f0079
com.example.tikoppij4:style/Base.Widget.AppCompat.ButtonBar = 0x7f0f0078
com.example.tikoppij4:style/Base.Widget.AppCompat.Button.Small = 0x7f0f0077
com.example.tikoppij4:style/Base.Widget.AppCompat.Button.Colored = 0x7f0f0076
com.example.tikoppij4:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0f0075
com.example.tikoppij4:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f0f0074
com.example.tikoppij4:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f0f006e
com.example.tikoppij4:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f0f006a
com.example.tikoppij4:style/Platform.AppCompat.Light = 0x7f0f00a7
com.example.tikoppij4:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f0f0066
com.example.tikoppij4:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f0f0064
com.example.tikoppij4:styleable/DrawerArrowToggle = 0x7f100016
com.example.tikoppij4:style/Theme.AppCompat.Light.Dialog = 0x7f0f0109
com.example.tikoppij4:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f0f0063
com.example.tikoppij4:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f0f0062
com.example.tikoppij4:style/Base.V28.Theme.AppCompat.Light = 0x7f0f005e
com.example.tikoppij4:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f0f005c
com.example.tikoppij4:style/Base.V23.Theme.AppCompat.Light = 0x7f0f0059
com.example.tikoppij4:style/Base.V23.Theme.AppCompat = 0x7f0f0058
com.example.tikoppij4:style/Base.V22.Theme.AppCompat = 0x7f0f0056
com.example.tikoppij4:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f0f0055
com.example.tikoppij4:style/Base.V21.Theme.AppCompat.Light = 0x7f0f0053
com.example.tikoppij4:style/Base.ThemeOverlay.AppCompat.Light = 0x7f0f0050
com.example.tikoppij4:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0f004f
com.example.tikoppij4:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f0f004e
com.example.tikoppij4:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f0f004b
com.example.tikoppij4:style/Base.V26.Theme.AppCompat.Light = 0x7f0f005b
com.example.tikoppij4:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f0f0049
com.example.tikoppij4:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f0f0046
com.example.tikoppij4:style/Base.Theme.AppCompat.Light.Dialog = 0x7f0f0045
com.example.tikoppij4:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f0f0042
com.example.tikoppij4:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f0f0041
com.example.tikoppij4:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f0f003f
com.example.tikoppij4:style/Base.Theme.AppCompat.Dialog = 0x7f0f003e
com.example.tikoppij4:style/Base.Widget.AppCompat.ActionButton = 0x7f0f006c
com.example.tikoppij4:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0f003b
com.example.tikoppij4:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0f003a
com.example.tikoppij4:style/Base.V7.Theme.AppCompat.Light = 0x7f0f0061
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0f0038
com.example.tikoppij4:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f0f00c0
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0f0036
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0f0035
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0f0034
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0f0033
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0f0031
com.example.tikoppij4:styleable/ListPopupWindow = 0x7f10001f
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0f0030
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f0f002f
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0f002e
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0f002c
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Title = 0x7f0f0025
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f0f0024
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f0f001e
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Menu = 0x7f0f001d
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f0f001c
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Medium = 0x7f0f001b
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0f001a
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0f0019
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0f00f0
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Large = 0x7f0f0017
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Inverse = 0x7f0f0016
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0f002a
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Headline = 0x7f0f0015
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Display3 = 0x7f0f0013
com.example.tikoppij4:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0f0048
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Display2 = 0x7f0f0012
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Caption = 0x7f0f0010
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Body2 = 0x7f0f000e
com.example.tikoppij4:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f0f0142
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Body1 = 0x7f0f000d
com.example.tikoppij4:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f0f000b
com.example.tikoppij4:style/Widget.AppCompat.ImageButton = 0x7f0f0131
com.example.tikoppij4:style/Base.DialogWindowTitle.AppCompat = 0x7f0f000a
com.example.tikoppij4:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f0f008d
com.example.tikoppij4:style/Base.Animation.AppCompat.Tooltip = 0x7f0f0009
com.example.tikoppij4:style/Base.Animation.AppCompat.Dialog = 0x7f0f0007
com.example.tikoppij4:style/Animation.AppCompat.Tooltip = 0x7f0f0004
com.example.tikoppij4:style/Animation.AppCompat.DropDownUp = 0x7f0f0003
com.example.tikoppij4:style/Widget.AppCompat.EditText = 0x7f0f0130
com.example.tikoppij4:style/AlertDialog.AppCompat.Light = 0x7f0f0001
com.example.tikoppij4:string/tooltip_label = 0x7f0e008c
com.example.tikoppij4:string/tooltip_description = 0x7f0e008b
com.example.tikoppij4:string/toggle_display_mode = 0x7f0e008a
com.example.tikoppij4:string/template_percent = 0x7f0e0088
com.example.tikoppij4:string/status_bar_notification_info_overflow = 0x7f0e0085
com.example.tikoppij4:style/TextAppearance.Compat.Notification = 0x7f0f00f1
com.example.tikoppij4:string/state_off = 0x7f0e0083
com.example.tikoppij4:style/Widget.AppCompat.AutoCompleteTextView = 0x7f0f0122
com.example.tikoppij4:string/snackbar_pane_title = 0x7f0e0081
com.example.tikoppij4:string/share_button = 0x7f0e0080
com.example.tikoppij4:string/selected = 0x7f0e007f
com.example.tikoppij4:string/range_start = 0x7f0e007d
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0f0032
com.example.tikoppij4:string/pause_video = 0x7f0e007a
com.example.tikoppij4:string/not_selected = 0x7f0e0079
com.example.tikoppij4:string/menu_button = 0x7f0e0077
com.example.tikoppij4:string/m3c_time_picker_pm = 0x7f0e0074
com.example.tikoppij4:string/m3c_time_picker_minute = 0x7f0e006f
com.example.tikoppij4:string/m3c_time_picker_hour_text_field = 0x7f0e006e
com.example.tikoppij4:string/switch_role = 0x7f0e0086
com.example.tikoppij4:string/m3c_time_picker_hour_24h_suffix = 0x7f0e006b
com.example.tikoppij4:string/m3c_suggestions_available = 0x7f0e0068
com.example.tikoppij4:string/m3c_snackbar_dismiss = 0x7f0e0067
com.example.tikoppij4:string/m3c_dropdown_menu_toggle = 0x7f0e0065
com.example.tikoppij4:styleable/Fragment = 0x7f100019
com.example.tikoppij4:styleable/AlertDialog = 0x7f100007
com.example.tikoppij4:string/m3c_dropdown_menu_collapsed = 0x7f0e0063
com.example.tikoppij4:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f0f006b
com.example.tikoppij4:string/m3c_dialog = 0x7f0e0062
com.example.tikoppij4:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f0f00bb
com.example.tikoppij4:string/m3c_date_range_picker_title = 0x7f0e0061
com.example.tikoppij4:string/m3c_date_range_picker_start_headline = 0x7f0e0060
com.example.tikoppij4:string/m3c_date_range_picker_scroll_to_previous_month = 0x7f0e005f
com.example.tikoppij4:string/m3c_date_range_picker_scroll_to_next_month = 0x7f0e005e
com.example.tikoppij4:string/m3c_date_range_picker_day_in_range = 0x7f0e005c
com.example.tikoppij4:string/m3c_date_range_input_title = 0x7f0e005b
com.example.tikoppij4:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0f0160
com.example.tikoppij4:string/m3c_date_picker_today_description = 0x7f0e0058
com.example.tikoppij4:string/m3c_date_picker_title = 0x7f0e0057
com.example.tikoppij4:string/m3c_date_picker_switch_to_year_selection = 0x7f0e0056
com.example.tikoppij4:string/m3c_date_picker_switch_to_next_month = 0x7f0e0054
com.example.tikoppij4:string/m3c_time_picker_minute_text_field = 0x7f0e0072
com.example.tikoppij4:string/m3c_date_picker_switch_to_day_selection = 0x7f0e0052
com.example.tikoppij4:string/m3c_date_picker_switch_to_calendar_mode = 0x7f0e0051
com.example.tikoppij4:string/m3c_date_picker_scroll_to_later_years = 0x7f0e0050
com.example.tikoppij4:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f0f00aa
com.example.tikoppij4:string/m3c_date_picker_headline_description = 0x7f0e004c
com.example.tikoppij4:styleable/PopupWindowBackgroundState = 0x7f10002b
com.example.tikoppij4:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f0f00fc
com.example.tikoppij4:string/m3c_time_picker_hour_suffix = 0x7f0e006d
com.example.tikoppij4:string/m3c_date_picker_headline = 0x7f0e004b
com.example.tikoppij4:string/m3c_date_input_title = 0x7f0e004a
com.example.tikoppij4:string/m3c_date_input_label = 0x7f0e0048
com.example.tikoppij4:string/m3c_date_input_invalid_not_allowed = 0x7f0e0046
com.example.tikoppij4:style/TextAppearance.AppCompat.Subhead = 0x7f0f00d9
com.example.tikoppij4:style/Base.V22.Theme.AppCompat.Light = 0x7f0f0057
com.example.tikoppij4:string/m3c_date_input_headline_description = 0x7f0e0044
com.example.tikoppij4:string/m3c_bottom_sheet_expand_description = 0x7f0e0041
com.example.tikoppij4:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0f010b
com.example.tikoppij4:string/m3c_bottom_sheet_drag_handle_description = 0x7f0e0040
com.example.tikoppij4:string/m3c_bottom_sheet_collapse_description = 0x7f0e003e
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0f00e9
com.example.tikoppij4:string/state_empty = 0x7f0e0082
com.example.tikoppij4:string/exo_download_paused = 0x7f0e0037
com.example.tikoppij4:string/exo_download_failed = 0x7f0e0035
com.example.tikoppij4:string/exo_download_downloading = 0x7f0e0034
com.example.tikoppij4:string/drawer_button = 0x7f0e0030
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Button = 0x7f0f000f
com.example.tikoppij4:string/display_mode_auto_adapt = 0x7f0e002c
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Display1 = 0x7f0f0011
com.example.tikoppij4:string/default_error_message = 0x7f0e002a
com.example.tikoppij4:string/call_notification_screening_text = 0x7f0e0027
com.example.tikoppij4:string/autofill = 0x7f0e0020
com.example.tikoppij4:string/auto_play_next_enabled = 0x7f0e001e
com.example.tikoppij4:string/auto_play_next_disabled = 0x7f0e001d
com.example.tikoppij4:string/app_name = 0x7f0e001c
com.example.tikoppij4:string/androidx_startup = 0x7f0e001b
com.example.tikoppij4:string/abc_shareactionprovider_share_with_application = 0x7f0e0019
com.example.tikoppij4:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0f012a
com.example.tikoppij4:string/abc_searchview_description_voice = 0x7f0e0017
com.example.tikoppij4:styleable/NavInclude = 0x7f100028
com.example.tikoppij4:string/abc_searchview_description_search = 0x7f0e0015
com.example.tikoppij4:string/abc_searchview_description_query = 0x7f0e0014
com.example.tikoppij4:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0f00d0
com.example.tikoppij4:string/abc_searchview_description_clear = 0x7f0e0013
com.example.tikoppij4:string/abc_menu_space_shortcut_label = 0x7f0e000f
com.example.tikoppij4:string/abc_menu_meta_shortcut_label = 0x7f0e000d
com.example.tikoppij4:styleable/ActionBarLayout = 0x7f100001
com.example.tikoppij4:string/abc_menu_function_shortcut_label = 0x7f0e000c
com.example.tikoppij4:string/call_notification_ongoing_text = 0x7f0e0026
com.example.tikoppij4:string/abc_menu_delete_shortcut_label = 0x7f0e000a
com.example.tikoppij4:string/m3c_date_input_no_input_description = 0x7f0e0049
com.example.tikoppij4:string/abc_menu_ctrl_shortcut_label = 0x7f0e0009
com.example.tikoppij4:string/abc_activity_chooser_view_see_all = 0x7f0e0004
com.example.tikoppij4:style/TextAppearance.AppCompat.Body1 = 0x7f0f00c2
com.example.tikoppij4:string/abc_action_mode_done = 0x7f0e0003
com.example.tikoppij4:style/Widget.AppCompat.ActivityChooserView = 0x7f0f0121
com.example.tikoppij4:string/abc_action_menu_overflow_description = 0x7f0e0002
com.example.tikoppij4:styleable/RecycleListView = 0x7f10002c
com.example.tikoppij4:mipmap/ic_launcher_round = 0x7f0d0001
com.example.tikoppij4:mipmap/ic_launcher = 0x7f0d0000
com.example.tikoppij4:layout/support_simple_spinner_dropdown_item = 0x7f0c0028
com.example.tikoppij4:layout/select_dialog_singlechoice_material = 0x7f0c0027
com.example.tikoppij4:layout/select_dialog_multichoice_material = 0x7f0c0026
com.example.tikoppij4:layout/select_dialog_item_material = 0x7f0c0025
com.example.tikoppij4:layout/notification_template_part_time = 0x7f0c0024
com.example.tikoppij4:layout/notification_template_part_chronometer = 0x7f0c0023
com.example.tikoppij4:styleable/MenuGroup = 0x7f100020
com.example.tikoppij4:layout/notification_template_custom_big = 0x7f0c0021
com.example.tikoppij4:layout/notification_action_tombstone = 0x7f0c0020
com.example.tikoppij4:style/Base.Widget.AppCompat.Spinner = 0x7f0f009b
com.example.tikoppij4:layout/ime_base_split_test_activity = 0x7f0c001d
com.example.tikoppij4:layout/abc_tooltip = 0x7f0c001b
com.example.tikoppij4:styleable/PopupWindow = 0x7f10002a
com.example.tikoppij4:layout/abc_search_view = 0x7f0c0019
com.example.tikoppij4:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0f004d
com.example.tikoppij4:layout/abc_screen_toolbar = 0x7f0c0017
com.example.tikoppij4:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f0f004c
com.example.tikoppij4:layout/abc_screen_simple_overlay_action_mode = 0x7f0c0016
com.example.tikoppij4:layout/abc_screen_simple = 0x7f0c0015
com.example.tikoppij4:layout/abc_screen_content_include = 0x7f0c0014
com.example.tikoppij4:layout/abc_popup_menu_header_item_layout = 0x7f0c0012
com.example.tikoppij4:string/download_button = 0x7f0e002f
com.example.tikoppij4:layout/abc_list_menu_item_radio = 0x7f0c0011
com.example.tikoppij4:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f0f0139
com.example.tikoppij4:layout/abc_list_menu_item_layout = 0x7f0c0010
com.example.tikoppij4:styleable/Navigator = 0x7f100029
com.example.tikoppij4:layout/abc_list_menu_item_icon = 0x7f0c000f
com.example.tikoppij4:layout/abc_expanded_menu_layout = 0x7f0c000d
com.example.tikoppij4:style/Widget.AppCompat.DrawerArrowToggle = 0x7f0f012e
com.example.tikoppij4:layout/abc_dialog_title_material = 0x7f0c000c
com.example.tikoppij4:layout/abc_alert_dialog_title_material = 0x7f0c000a
com.example.tikoppij4:layout/abc_alert_dialog_material = 0x7f0c0009
com.example.tikoppij4:layout/abc_alert_dialog_button_bar_material = 0x7f0c0008
com.example.tikoppij4:layout/abc_activity_chooser_view_list_item = 0x7f0c0007
com.example.tikoppij4:layout/abc_action_menu_layout = 0x7f0c0003
com.example.tikoppij4:layout/abc_action_bar_title_item = 0x7f0c0000
com.example.tikoppij4:interpolator/fast_out_slow_in = 0x7f0b0006
com.example.tikoppij4:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0b0003
com.example.tikoppij4:layout/abc_search_dropdown_item_icons_2line = 0x7f0c0018
com.example.tikoppij4:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0b0002
com.example.tikoppij4:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0b0000
com.example.tikoppij4:integer/status_bar_notification_info_maxnum = 0x7f0a0005
com.example.tikoppij4:integer/m3c_window_layout_in_display_cutout_mode = 0x7f0a0004
com.example.tikoppij4:integer/config_tooltipAnimTime = 0x7f0a0003
com.example.tikoppij4:integer/cancel_button_image_alpha = 0x7f0a0002
com.example.tikoppij4:integer/abc_config_activityShortDur = 0x7f0a0001
com.example.tikoppij4:id/wrapped_composition_tag = 0x7f0900c0
com.example.tikoppij4:id/wrap_content = 0x7f0900bf
com.example.tikoppij4:id/visible_removing_fragment_view_tag = 0x7f0900bd
com.example.tikoppij4:id/view_tree_view_model_store_owner = 0x7f0900bc
com.example.tikoppij4:id/view_tree_saved_state_registry_owner = 0x7f0900bb
com.example.tikoppij4:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f0f0040
com.example.tikoppij4:id/view_tree_lifecycle_owner = 0x7f0900b9
com.example.tikoppij4:id/view_tree_disjoint_parent = 0x7f0900b8
com.example.tikoppij4:id/up = 0x7f0900b6
com.example.tikoppij4:id/unchecked = 0x7f0900b4
com.example.tikoppij4:string/abc_action_bar_home_description = 0x7f0e0000
com.example.tikoppij4:id/top = 0x7f0900b2
com.example.tikoppij4:styleable/Toolbar = 0x7f100033
com.example.tikoppij4:id/title_template = 0x7f0900b1
com.example.tikoppij4:id/titleDividerNoCustom = 0x7f0900b0
com.example.tikoppij4:id/title = 0x7f0900af
com.example.tikoppij4:style/TextAppearance.AppCompat.Tooltip = 0x7f0f00dd
com.example.tikoppij4:id/time = 0x7f0900ae
com.example.tikoppij4:id/textSpacerNoTitle = 0x7f0900ad
com.example.tikoppij4:style/Widget.AppCompat.ActionBar.TabView = 0x7f0f011c
com.example.tikoppij4:id/textSpacerNoButtons = 0x7f0900ac
com.example.tikoppij4:id/text = 0x7f0900aa
com.example.tikoppij4:id/tag_window_insets_animation_callback = 0x7f0900a9
com.example.tikoppij4:id/tag_unhandled_key_listeners = 0x7f0900a8
com.example.tikoppij4:style/Base.ThemeOverlay.AppCompat = 0x7f0f004a
com.example.tikoppij4:id/tag_transition_group = 0x7f0900a6
com.example.tikoppij4:id/tag_state_description = 0x7f0900a4
com.example.tikoppij4:id/tag_on_receive_content_mime_types = 0x7f0900a2
com.example.tikoppij4:id/tag_on_receive_content_listener = 0x7f0900a1
com.example.tikoppij4:id/tag_on_apply_window_listener = 0x7f0900a0
com.example.tikoppij4:id/tag_compat_insets_dispatch = 0x7f09009f
com.example.tikoppij4:id/tag_accessibility_pane_title = 0x7f09009e
com.example.tikoppij4:id/tag_accessibility_heading = 0x7f09009d
com.example.tikoppij4:id/tag_accessibility_actions = 0x7f09009b
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Subhead = 0x7f0f0023
com.example.tikoppij4:id/tabMode = 0x7f09009a
com.example.tikoppij4:id/submit_area = 0x7f090099
com.example.tikoppij4:styleable/GradientColorItem = 0x7f10001c
com.example.tikoppij4:id/submenuarrow = 0x7f090098
com.example.tikoppij4:id/src_atop = 0x7f090095
com.example.tikoppij4:id/split_action_bar = 0x7f090094
com.example.tikoppij4:id/special_effects_controller_view_tag = 0x7f090093
com.example.tikoppij4:style/Base.Widget.AppCompat.SeekBar = 0x7f0f0099
com.example.tikoppij4:id/spacer = 0x7f090092
com.example.tikoppij4:id/shortcut = 0x7f09008e
com.example.tikoppij4:styleable/FontFamilyFont = 0x7f100018
com.example.tikoppij4:id/select_dialog_listview = 0x7f09008d
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0f002d
com.example.tikoppij4:id/search_voice_btn = 0x7f09008c
com.example.tikoppij4:id/search_plate = 0x7f09008a
com.example.tikoppij4:id/search_mag_icon = 0x7f090089
com.example.tikoppij4:id/search_go_btn = 0x7f090088
com.example.tikoppij4:style/Widget.AppCompat.ActionButton = 0x7f0f011d
com.example.tikoppij4:id/search_edit_frame = 0x7f090087
com.example.tikoppij4:style/Base.Theme.AppCompat.Light = 0x7f0f0043
com.example.tikoppij4:id/search_close_btn = 0x7f090086
com.example.tikoppij4:id/search_bar = 0x7f090084
com.example.tikoppij4:style/Theme.AppCompat.DialogWhenLarge = 0x7f0f0105
com.example.tikoppij4:id/search_badge = 0x7f090083
com.example.tikoppij4:id/scrollView = 0x7f090082
com.example.tikoppij4:id/scrollIndicatorUp = 0x7f090081
com.example.tikoppij4:id/scrollIndicatorDown = 0x7f090080
com.example.tikoppij4:id/showCustom = 0x7f09008f
com.example.tikoppij4:id/screen = 0x7f09007f
com.example.tikoppij4:id/right_icon = 0x7f09007d
com.example.tikoppij4:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f0f0071
com.example.tikoppij4:id/progress_horizontal = 0x7f09007a
com.example.tikoppij4:style/Base.V26.Theme.AppCompat = 0x7f0f005a
com.example.tikoppij4:string/abc_searchview_description_submit = 0x7f0e0016
com.example.tikoppij4:id/parentPanel = 0x7f090077
com.example.tikoppij4:style/Base.Theme.AppCompat.CompactMenu = 0x7f0f003d
com.example.tikoppij4:id/on = 0x7f090076
com.example.tikoppij4:id/notification_main_column = 0x7f090073
com.example.tikoppij4:id/notification_background = 0x7f090072
com.example.tikoppij4:id/never = 0x7f09006f
com.example.tikoppij4:id/nav_controller_view_tag = 0x7f09006e
com.example.tikoppij4:id/multiply = 0x7f09006d
com.example.tikoppij4:id/message = 0x7f09006b
com.example.tikoppij4:id/listMode = 0x7f090069
com.example.tikoppij4:id/italic = 0x7f090066
com.example.tikoppij4:id/is_pooling_container_tag = 0x7f090065
com.example.tikoppij4:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f0f012f
com.example.tikoppij4:dimen/highlight_alpha_material_dark = 0x7f06005b
com.example.tikoppij4:attr/measureWithLargestChild = 0x7f0300bc
com.example.tikoppij4:color/white = 0x7f050061
com.example.tikoppij4:id/inspection_slot_table_set = 0x7f090064
com.example.tikoppij4:style/Theme.AppCompat.Dialog.Alert = 0x7f0f0103
com.example.tikoppij4:string/m3c_date_picker_year_picker_pane_title = 0x7f0e0059
com.example.tikoppij4:id/info = 0x7f090063
com.example.tikoppij4:attr/actionProviderClass = 0x7f030022
com.example.tikoppij4:id/image = 0x7f090062
com.example.tikoppij4:string/m3c_bottom_sheet_pane_title = 0x7f0e0042
com.example.tikoppij4:id/notification_main_column_container = 0x7f090074
com.example.tikoppij4:color/teal_200 = 0x7f05005b
com.example.tikoppij4:id/hide_graphics_layer_in_inspector_tag = 0x7f09005a
com.example.tikoppij4:style/TextAppearance.AppCompat.Title.Inverse = 0x7f0f00dc
com.example.tikoppij4:layout/abc_select_dialog_material = 0x7f0c001a
com.example.tikoppij4:id/group_divider = 0x7f090059
com.example.tikoppij4:id/forever = 0x7f090057
com.example.tikoppij4:id/expand_activities_button = 0x7f090055
com.example.tikoppij4:styleable/ViewStubCompat = 0x7f100036
com.example.tikoppij4:id/end = 0x7f090054
com.example.tikoppij4:id/disableHome = 0x7f090051
com.example.tikoppij4:attr/font = 0x7f030088
com.example.tikoppij4:id/default_activity_button = 0x7f09004f
com.example.tikoppij4:id/custom = 0x7f09004c
com.example.tikoppij4:color/abc_tint_switch_track = 0x7f050018
com.example.tikoppij4:id/consume_window_insets_tag = 0x7f090049
com.example.tikoppij4:id/compose_view_saveable_id_tag = 0x7f090048
com.example.tikoppij4:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0f00a0
com.example.tikoppij4:id/checked = 0x7f090045
com.example.tikoppij4:id/center_vertical = 0x7f090043
com.example.tikoppij4:id/buttonPanel = 0x7f090042
com.example.tikoppij4:attr/title = 0x7f030118
com.example.tikoppij4:id/bottom = 0x7f090041
com.example.tikoppij4:layout/notification_template_icon_group = 0x7f0c0022
com.example.tikoppij4:id/alertTitle = 0x7f09003b
com.example.tikoppij4:id/action_mode_close_button = 0x7f090036
com.example.tikoppij4:attr/startDestination = 0x7f0300f4
com.example.tikoppij4:id/action_image = 0x7f090031
com.example.tikoppij4:id/action_context_bar = 0x7f09002f
com.example.tikoppij4:attr/fontWeight = 0x7f030094
com.example.tikoppij4:drawable/abc_list_focused_holo = 0x7f070027
com.example.tikoppij4:id/action_bar_container = 0x7f090029
com.example.tikoppij4:styleable/AppCompatTextHelper = 0x7f10000e
com.example.tikoppij4:id/action_bar = 0x7f090027
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f0f00e6
com.example.tikoppij4:anim/abc_slide_out_bottom = 0x7f010008
com.example.tikoppij4:id/accessibility_custom_action_4 = 0x7f090021
com.example.tikoppij4:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f0f012c
com.example.tikoppij4:attr/subMenuArrow = 0x7f0300f6
com.example.tikoppij4:id/accessibility_custom_action_30 = 0x7f09001f
com.example.tikoppij4:drawable/test_level_drawable = 0x7f070080
com.example.tikoppij4:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.example.tikoppij4:id/accessibility_custom_action_3 = 0x7f09001e
com.example.tikoppij4:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
com.example.tikoppij4:id/accessibility_custom_action_29 = 0x7f09001d
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0f00e1
com.example.tikoppij4:id/accessibility_custom_action_26 = 0x7f09001a
com.example.tikoppij4:id/accessibility_custom_action_23 = 0x7f090017
com.example.tikoppij4:attr/contentInsetStartWithNavigation = 0x7f030064
com.example.tikoppij4:id/accessibility_custom_action_20 = 0x7f090014
com.example.tikoppij4:layout/abc_action_mode_close_item_material = 0x7f0c0005
com.example.tikoppij4:id/accessibility_custom_action_19 = 0x7f090012
com.example.tikoppij4:id/accessibility_custom_action_18 = 0x7f090011
com.example.tikoppij4:dimen/abc_list_item_height_material = 0x7f060031
com.example.tikoppij4:id/accessibility_custom_action_16 = 0x7f09000f
com.example.tikoppij4:styleable/ViewBackgroundHelper = 0x7f100035
com.example.tikoppij4:string/favorite_button = 0x7f0e003b
com.example.tikoppij4:attr/textAppearanceSearchResultSubtitle = 0x7f030108
com.example.tikoppij4:id/accessibility_custom_action_15 = 0x7f09000e
com.example.tikoppij4:id/accessibility_custom_action_13 = 0x7f09000c
com.example.tikoppij4:id/accessibility_custom_action_11 = 0x7f09000a
com.example.tikoppij4:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f0f0100
com.example.tikoppij4:attr/subtitleTextColor = 0x7f0300fa
com.example.tikoppij4:id/accessibility_custom_action_10 = 0x7f090009
com.example.tikoppij4:id/accessibility_custom_action_0 = 0x7f090007
com.example.tikoppij4:id/META = 0x7f090003
com.example.tikoppij4:id/ALT = 0x7f090000
com.example.tikoppij4:drawable/tooltip_frame_dark = 0x7f070081
com.example.tikoppij4:string/display_mode_setting = 0x7f0e002e
com.example.tikoppij4:attr/ratingBarStyleIndicator = 0x7f0300df
com.example.tikoppij4:color/tooltip_background_dark = 0x7f05005d
com.example.tikoppij4:drawable/notification_tile_bg = 0x7f07007e
com.example.tikoppij4:drawable/ic_arrow_back = 0x7f070058
com.example.tikoppij4:drawable/notification_bg_normal = 0x7f070078
com.example.tikoppij4:attr/buttonBarButtonStyle = 0x7f03003c
com.example.tikoppij4:drawable/notification_bg_low_pressed = 0x7f070077
com.example.tikoppij4:drawable/notification_bg_low_normal = 0x7f070076
com.example.tikoppij4:attr/checkedTextViewStyle = 0x7f03004d
com.example.tikoppij4:drawable/notification_bg = 0x7f070074
com.example.tikoppij4:drawable/ic_tools = 0x7f070070
com.example.tikoppij4:drawable/ic_profile = 0x7f07006d
com.example.tikoppij4:drawable/ic_play_arrow = 0x7f07006c
com.example.tikoppij4:drawable/ic_pause = 0x7f07006a
com.example.tikoppij4:string/exo_download_notification_channel_name = 0x7f0e0036
com.example.tikoppij4:layout/ime_secondary_split_test_activity = 0x7f0c001e
com.example.tikoppij4:drawable/ic_menu = 0x7f070069
com.example.tikoppij4:drawable/ic_history = 0x7f070065
com.example.tikoppij4:id/line3 = 0x7f090068
com.example.tikoppij4:drawable/ic_hidden = 0x7f070064
com.example.tikoppij4:style/TextAppearance.AppCompat.Medium = 0x7f0f00d2
com.example.tikoppij4:color/primary_material_dark = 0x7f050046
com.example.tikoppij4:id/action_menu_divider = 0x7f090032
com.example.tikoppij4:attr/mimeType = 0x7f0300be
com.example.tikoppij4:drawable/ic_delete = 0x7f070060
com.example.tikoppij4:color/error_color_material_dark = 0x7f050030
com.example.tikoppij4:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f07001b
com.example.tikoppij4:drawable/ic_call_decline_low = 0x7f07005f
com.example.tikoppij4:font/douyin_sansbold = 0x7f080001
com.example.tikoppij4:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f0f0093
com.example.tikoppij4:string/m3c_date_range_picker_end_headline = 0x7f0e005d
com.example.tikoppij4:layout/abc_action_bar_up_container = 0x7f0c0001
com.example.tikoppij4:drawable/ic_call_decline = 0x7f07005e
com.example.tikoppij4:drawable/ic_download = 0x7f070061
com.example.tikoppij4:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f0f0147
com.example.tikoppij4:attr/icon = 0x7f03009c
com.example.tikoppij4:drawable/ic_call_answer_video_low = 0x7f07005d
com.example.tikoppij4:drawable/ic_call_answer = 0x7f07005a
com.example.tikoppij4:id/action_bar_activity_content = 0x7f090028
com.example.tikoppij4:drawable/abc_ic_ab_back_material = 0x7f070016
com.example.tikoppij4:drawable/btn_radio_on_mtrl = 0x7f070056
com.example.tikoppij4:styleable/StateListDrawable = 0x7f10002f
com.example.tikoppij4:style/Widget.AppCompat.ProgressBar = 0x7f0f0150
com.example.tikoppij4:color/switch_thumb_normal_material_light = 0x7f05005a
com.example.tikoppij4:attr/contentInsetRight = 0x7f030062
com.example.tikoppij4:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f070053
com.example.tikoppij4:attr/commitIcon = 0x7f03005d
com.example.tikoppij4:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f07004d
com.example.tikoppij4:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f0f00b2
com.example.tikoppij4:dimen/abc_button_padding_horizontal_material = 0x7f060014
com.example.tikoppij4:id/action_mode_bar = 0x7f090034
com.example.tikoppij4:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f07004c
com.example.tikoppij4:attr/route = 0x7f0300e2
com.example.tikoppij4:color/dim_foreground_material_light = 0x7f05002f
com.example.tikoppij4:drawable/abc_textfield_activated_mtrl_alpha = 0x7f07004a
com.example.tikoppij4:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f0f00bd
com.example.tikoppij4:attr/allowStacking = 0x7f030029
com.example.tikoppij4:drawable/abc_text_select_handle_middle_mtrl = 0x7f070048
com.example.tikoppij4:drawable/abc_vector_test = 0x7f07004f
com.example.tikoppij4:drawable/abc_text_select_handle_left_mtrl = 0x7f070047
com.example.tikoppij4:attr/paddingTopNoTitle = 0x7f0300cb
com.example.tikoppij4:attr/listDividerAlertDialog = 0x7f0300ad
com.example.tikoppij4:id/accessibility_custom_action_24 = 0x7f090018
com.example.tikoppij4:drawable/abc_switch_track_mtrl_alpha = 0x7f070043
com.example.tikoppij4:style/TextAppearance.AppCompat.Display2 = 0x7f0f00c7
com.example.tikoppij4:drawable/abc_switch_thumb_material = 0x7f070042
com.example.tikoppij4:dimen/notification_content_margin_start = 0x7f060064
com.example.tikoppij4:drawable/abc_star_black_48dp = 0x7f070040
com.example.tikoppij4:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f0f0069
com.example.tikoppij4:font/dingtalk_jinbuti = 0x7f080000
com.example.tikoppij4:dimen/notification_small_icon_background_padding = 0x7f06006b
com.example.tikoppij4:drawable/abc_spinner_textfield_background_material = 0x7f07003f
com.example.tikoppij4:string/m3c_date_input_headline = 0x7f0e0043
com.example.tikoppij4:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.example.tikoppij4:attr/progressBarStyle = 0x7f0300d9
com.example.tikoppij4:drawable/abc_spinner_mtrl_am_alpha = 0x7f07003e
com.example.tikoppij4:drawable/abc_seekbar_track_material = 0x7f07003d
com.example.tikoppij4:styleable/ActionMenuItemView = 0x7f100002
com.example.tikoppij4:id/edit_query = 0x7f090052
com.example.tikoppij4:style/Platform.V21.AppCompat = 0x7f0f00ab
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f0f0037
com.example.tikoppij4:id/text2 = 0x7f0900ab
com.example.tikoppij4:drawable/abc_seekbar_thumb_material = 0x7f07003b
com.example.tikoppij4:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f070039
com.example.tikoppij4:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f070037
com.example.tikoppij4:drawable/abc_ratingbar_small_material = 0x7f070035
com.example.tikoppij4:drawable/abc_list_selector_holo_light = 0x7f070030
com.example.tikoppij4:attr/navigationMode = 0x7f0300c3
com.example.tikoppij4:drawable/abc_list_selector_holo_dark = 0x7f07002f
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0f00ee
com.example.tikoppij4:id/SHIFT = 0x7f090004
com.example.tikoppij4:id/beginning = 0x7f09003f
com.example.tikoppij4:attr/buttonPanelSideLayout = 0x7f030044
com.example.tikoppij4:drawable/abc_list_selector_disabled_holo_dark = 0x7f07002d
com.example.tikoppij4:color/highlighted_text_material_dark = 0x7f050034
com.example.tikoppij4:attr/tickMark = 0x7f030113
com.example.tikoppij4:drawable/abc_list_pressed_holo_light = 0x7f07002a
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.Button = 0x7f0f00e7
com.example.tikoppij4:style/Base.AlertDialog.AppCompat.Light = 0x7f0f0006
com.example.tikoppij4:drawable/abc_ic_voice_search_api_material = 0x7f070022
com.example.tikoppij4:attr/autoCompleteTextViewStyle = 0x7f03002f
com.example.tikoppij4:drawable/ic_call_answer_video = 0x7f07005c
com.example.tikoppij4:style/Widget.AppCompat.ListView = 0x7f0f014a
com.example.tikoppij4:string/close_sheet = 0x7f0e0029
com.example.tikoppij4:color/dim_foreground_disabled_material_light = 0x7f05002d
com.example.tikoppij4:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f070020
com.example.tikoppij4:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f07001f
com.example.tikoppij4:id/accessibility_custom_action_17 = 0x7f090010
com.example.tikoppij4:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f07001e
com.example.tikoppij4:drawable/abc_ic_go_search_api_material = 0x7f07001a
com.example.tikoppij4:id/actions = 0x7f090038
com.example.tikoppij4:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f070019
com.example.tikoppij4:styleable/Spinner = 0x7f10002e
com.example.tikoppij4:color/black = 0x7f050021
com.example.tikoppij4:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f070017
com.example.tikoppij4:integer/abc_config_activityDefaultDur = 0x7f0a0000
com.example.tikoppij4:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070031
com.example.tikoppij4:drawable/ic_settings = 0x7f07006e
com.example.tikoppij4:style/Base.Widget.AppCompat.PopupMenu = 0x7f0f008f
com.example.tikoppij4:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f07001c
com.example.tikoppij4:drawable/abc_dialog_material_background = 0x7f070014
com.example.tikoppij4:id/action_bar_spinner = 0x7f09002b
com.example.tikoppij4:drawable/abc_control_background_material = 0x7f070013
com.example.tikoppij4:drawable/abc_cab_background_top_mtrl_alpha = 0x7f070012
com.example.tikoppij4:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f07000f
com.example.tikoppij4:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f07000e
com.example.tikoppij4:dimen/abc_action_button_min_height_material = 0x7f06000d
com.example.tikoppij4:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f07000d
com.example.tikoppij4:drawable/abc_btn_radio_material = 0x7f07000a
com.example.tikoppij4:id/normal = 0x7f090071
com.example.tikoppij4:drawable/btn_radio_off_mtrl = 0x7f070054
com.example.tikoppij4:drawable/abc_btn_default_mtrl_shape = 0x7f070009
com.example.tikoppij4:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f070006
com.example.tikoppij4:drawable/abc_btn_check_material = 0x7f070004
com.example.tikoppij4:attr/drawableTintMode = 0x7f03007a
com.example.tikoppij4:drawable/abc_tab_indicator_material = 0x7f070044
com.example.tikoppij4:drawable/abc_btn_borderless_material = 0x7f070003
com.example.tikoppij4:drawable/abc_action_bar_item_background_material = 0x7f070002
com.example.tikoppij4:style/Theme.AppCompat.Empty = 0x7f0f0106
com.example.tikoppij4:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070001
com.example.tikoppij4:dimen/tooltip_y_offset_non_touch = 0x7f060076
com.example.tikoppij4:attr/dividerVertical = 0x7f030072
com.example.tikoppij4:color/abc_hint_foreground_material_light = 0x7f050008
com.example.tikoppij4:drawable/notification_oversize_large_icon_bg = 0x7f07007b
com.example.tikoppij4:dimen/tooltip_margin = 0x7f060072
com.example.tikoppij4:drawable/tooltip_frame_light = 0x7f070082
com.example.tikoppij4:dimen/tooltip_horizontal_padding = 0x7f060071
com.example.tikoppij4:dimen/notification_right_side_padding_top = 0x7f06006a
com.example.tikoppij4:attr/layout = 0x7f0300a8
com.example.tikoppij4:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f070007
com.example.tikoppij4:dimen/notification_right_icon_size = 0x7f060069
com.example.tikoppij4:dimen/notification_media_narrow_margin = 0x7f060068
com.example.tikoppij4:attr/backgroundSplit = 0x7f030036
com.example.tikoppij4:attr/tintMode = 0x7f030117
com.example.tikoppij4:dimen/notification_large_icon_height = 0x7f060065
com.example.tikoppij4:attr/barLength = 0x7f03003a
com.example.tikoppij4:id/collapseActionView = 0x7f090047
com.example.tikoppij4:string/m3c_date_picker_scroll_to_earlier_years = 0x7f0e004f
com.example.tikoppij4:string/abc_prepend_shortcut_label = 0x7f0e0011
com.example.tikoppij4:dimen/notification_big_circle_margin = 0x7f060063
com.example.tikoppij4:dimen/notification_action_icon_size = 0x7f060061
com.example.tikoppij4:dimen/hint_pressed_alpha_material_light = 0x7f060060
com.example.tikoppij4:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f0f00fe
com.example.tikoppij4:dimen/highlight_alpha_material_light = 0x7f06005c
com.example.tikoppij4:dimen/highlight_alpha_material_colored = 0x7f06005a
com.example.tikoppij4:id/FUNCTION = 0x7f090002
com.example.tikoppij4:attr/splitTrack = 0x7f0300f2
com.example.tikoppij4:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.example.tikoppij4:dimen/disabled_alpha_material_dark = 0x7f060058
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0f001f
com.example.tikoppij4:string/m3c_date_range_input_invalid_range_input = 0x7f0e005a
com.example.tikoppij4:dimen/compat_notification_large_icon_max_width = 0x7f060057
com.example.tikoppij4:dimen/compat_notification_large_icon_max_height = 0x7f060056
com.example.tikoppij4:attr/actionModeCopyDrawable = 0x7f030015
com.example.tikoppij4:drawable/notify_panel_notification_icon_bg = 0x7f07007f
com.example.tikoppij4:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f07000c
com.example.tikoppij4:dimen/compat_button_padding_vertical_material = 0x7f060054
com.example.tikoppij4:styleable/NavHost = 0x7f100027
com.example.tikoppij4:style/Widget.AppCompat.Button = 0x7f0f0123
com.example.tikoppij4:drawable/ic_play = 0x7f07006b
com.example.tikoppij4:drawable/abc_text_cursor_material = 0x7f070046
com.example.tikoppij4:drawable/$ic_launcher_foreground__0 = 0x7f070000
com.example.tikoppij4:dimen/compat_button_inset_vertical_material = 0x7f060052
com.example.tikoppij4:dimen/abc_text_size_title_material_toolbar = 0x7f060050
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f0f0020
com.example.tikoppij4:dimen/abc_text_size_title_material = 0x7f06004f
com.example.tikoppij4:drawable/abc_item_background_holo_light = 0x7f070024
com.example.tikoppij4:style/TextAppearance.AppCompat.Button = 0x7f0f00c4
com.example.tikoppij4:id/blocking = 0x7f090040
com.example.tikoppij4:attr/buttonTint = 0x7f030047
com.example.tikoppij4:attr/listPreferredItemPaddingEnd = 0x7f0300b5
com.example.tikoppij4:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
com.example.tikoppij4:attr/buttonIconDimen = 0x7f030043
com.example.tikoppij4:drawable/ic_call_answer_low = 0x7f07005b
com.example.tikoppij4:dimen/abc_text_size_subhead_material = 0x7f06004d
com.example.tikoppij4:id/uniform = 0x7f0900b5
com.example.tikoppij4:attr/navigationContentDescription = 0x7f0300c1
com.example.tikoppij4:dimen/abc_text_size_small_material = 0x7f06004c
com.example.tikoppij4:dimen/abc_text_size_menu_material = 0x7f06004b
com.example.tikoppij4:drawable/abc_list_longpressed_holo = 0x7f070028
com.example.tikoppij4:drawable/abc_ratingbar_indicator_material = 0x7f070033
com.example.tikoppij4:dimen/hint_alpha_material_dark = 0x7f06005d
com.example.tikoppij4:id/androidx_compose_ui_view_composition_context = 0x7f09003d
com.example.tikoppij4:dimen/abc_text_size_medium_material = 0x7f060049
com.example.tikoppij4:dimen/abc_text_size_large_material = 0x7f060048
com.example.tikoppij4:attr/actionBarTabBarStyle = 0x7f030007
com.example.tikoppij4:dimen/abc_text_size_headline_material = 0x7f060047
com.example.tikoppij4:style/Theme.AppCompat.Dialog.MinWidth = 0x7f0f0104
com.example.tikoppij4:drawable/abc_tab_indicator_mtrl_alpha = 0x7f070045
com.example.tikoppij4:attr/dialogPreferredPadding = 0x7f03006c
com.example.tikoppij4:drawable/abc_btn_colored_material = 0x7f070008
com.example.tikoppij4:style/Widget.AppCompat.Spinner.Underlined = 0x7f0f015c
com.example.tikoppij4:color/button_material_dark = 0x7f050028
com.example.tikoppij4:dimen/abc_text_size_display_2_material = 0x7f060044
com.example.tikoppij4:dimen/abc_text_size_caption_material = 0x7f060042
com.example.tikoppij4:dimen/abc_text_size_body_1_material = 0x7f06003f
com.example.tikoppij4:drawable/abc_seekbar_tick_mark_material = 0x7f07003c
com.example.tikoppij4:id/tag_system_bar_state_monitor = 0x7f0900a5
com.example.tikoppij4:dimen/abc_switch_padding = 0x7f06003e
com.example.tikoppij4:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
com.example.tikoppij4:styleable/ActionMenuView = 0x7f100003
com.example.tikoppij4:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
com.example.tikoppij4:drawable/btn_checkbox_unchecked_mtrl = 0x7f070052
com.example.tikoppij4:dimen/abc_seekbar_track_background_height_material = 0x7f060038
com.example.tikoppij4:attr/arrowShaftLength = 0x7f03002e
com.example.tikoppij4:attr/titleTextColor = 0x7f030120
com.example.tikoppij4:dimen/abc_search_view_preferred_width = 0x7f060037
com.example.tikoppij4:style/Base.Widget.AppCompat.Toolbar = 0x7f0f009f
com.example.tikoppij4:drawable/btn_checkbox_checked_mtrl = 0x7f070050
com.example.tikoppij4:attr/buttonBarPositiveButtonStyle = 0x7f03003f
com.example.tikoppij4:dimen/abc_search_view_preferred_height = 0x7f060036
com.example.tikoppij4:dimen/tooltip_y_offset_touch = 0x7f060077
com.example.tikoppij4:string/tab = 0x7f0e0087
com.example.tikoppij4:dimen/abc_progress_bar_height_material = 0x7f060035
com.example.tikoppij4:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f0f015b
com.example.tikoppij4:string/abc_menu_shift_shortcut_label = 0x7f0e000e
com.example.tikoppij4:dimen/abc_panel_menu_list_width = 0x7f060034
com.example.tikoppij4:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
com.example.tikoppij4:dimen/abc_list_item_height_large_material = 0x7f060030
com.example.tikoppij4:id/accessibility_custom_action_1 = 0x7f090008
com.example.tikoppij4:dimen/abc_floating_window_z = 0x7f06002f
com.example.tikoppij4:style/Platform.ThemeOverlay.AppCompat = 0x7f0f00a8
com.example.tikoppij4:dimen/abc_edit_text_inset_top_material = 0x7f06002e
com.example.tikoppij4:id/content = 0x7f09004a
com.example.tikoppij4:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
com.example.tikoppij4:style/Base.TextAppearance.AppCompat = 0x7f0f000c
com.example.tikoppij4:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
com.example.tikoppij4:string/exo_download_paused_for_network = 0x7f0e0038
com.example.tikoppij4:dimen/abc_dropdownitem_icon_width = 0x7f060029
com.example.tikoppij4:color/notification_icon_bg_color = 0x7f050043
com.example.tikoppij4:id/chronometer = 0x7f090046
com.example.tikoppij4:dimen/abc_disabled_alpha_material_light = 0x7f060028
com.example.tikoppij4:drawable/abc_popup_background_mtrl_mult = 0x7f070032
com.example.tikoppij4:id/action_bar_title = 0x7f09002d
com.example.tikoppij4:attr/restoreState = 0x7f0300e1
com.example.tikoppij4:dimen/abc_disabled_alpha_material_dark = 0x7f060027
com.example.tikoppij4:attr/colorPrimaryDark = 0x7f03005b
com.example.tikoppij4:dimen/abc_text_size_display_3_material = 0x7f060045
com.example.tikoppij4:styleable/CheckedTextView = 0x7f100013
com.example.tikoppij4:dimen/abc_dialog_title_divider_material = 0x7f060026
com.example.tikoppij4:id/edit_text_id = 0x7f090053
com.example.tikoppij4:dimen/abc_dialog_padding_top_material = 0x7f060025
com.example.tikoppij4:id/accessibility_custom_action_31 = 0x7f090020
com.example.tikoppij4:dimen/abc_dialog_padding_material = 0x7f060024
com.example.tikoppij4:style/Base.Widget.AppCompat.ActionMode = 0x7f0f006f
com.example.tikoppij4:id/icon = 0x7f09005f
com.example.tikoppij4:dimen/abc_dialog_min_width_minor = 0x7f060023
com.example.tikoppij4:anim/fragment_fast_out_extra_slow_in = 0x7f010018
com.example.tikoppij4:dimen/abc_dialog_min_width_major = 0x7f060022
com.example.tikoppij4:drawable/abc_textfield_search_material = 0x7f07004e
com.example.tikoppij4:drawable/notification_icon_background = 0x7f07007a
com.example.tikoppij4:dimen/notification_small_icon_size_as_large = 0x7f06006c
com.example.tikoppij4:attr/windowActionModeOverlay = 0x7f030130
com.example.tikoppij4:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
com.example.tikoppij4:id/middle = 0x7f09006c
com.example.tikoppij4:attr/dropdownListPreferredItemHeight = 0x7f03007e
com.example.tikoppij4:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
com.example.tikoppij4:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
com.example.tikoppij4:id/accessibility_custom_action_25 = 0x7f090019
com.example.tikoppij4:id/dialog_button = 0x7f090050
com.example.tikoppij4:dimen/abc_control_padding_material = 0x7f06001a
com.example.tikoppij4:drawable/ic_launcher_foreground = 0x7f070068
com.example.tikoppij4:dimen/abc_control_corner_material = 0x7f060018
com.example.tikoppij4:dimen/abc_button_padding_vertical_material = 0x7f060015
com.example.tikoppij4:id/accessibility_custom_action_27 = 0x7f09001b
com.example.tikoppij4:drawable/ic_home = 0x7f070066
com.example.tikoppij4:dimen/abc_button_inset_horizontal_material = 0x7f060012
com.example.tikoppij4:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
com.example.tikoppij4:attr/windowMinWidthMinor = 0x7f030136
com.example.tikoppij4:id/accessibility_custom_action_7 = 0x7f090024
com.example.tikoppij4:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0f00f8
com.example.tikoppij4:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
com.example.tikoppij4:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
com.example.tikoppij4:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
com.example.tikoppij4:dimen/abc_text_size_body_2_material = 0x7f060040
com.example.tikoppij4:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f0f0027
com.example.tikoppij4:id/always = 0x7f09003c
com.example.tikoppij4:attr/logo = 0x7f0300b9
com.example.tikoppij4:dimen/abc_action_button_min_width_material = 0x7f06000e
com.example.tikoppij4:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
com.example.tikoppij4:dimen/abc_action_bar_default_height_material = 0x7f060002
com.example.tikoppij4:string/call_notification_answer_action = 0x7f0e0021
com.example.tikoppij4:color/tooltip_background_light = 0x7f05005e
com.example.tikoppij4:color/teal_700 = 0x7f05005c
com.example.tikoppij4:color/switch_thumb_normal_material_dark = 0x7f050059
com.example.tikoppij4:color/switch_thumb_material_dark = 0x7f050057
com.example.tikoppij4:color/switch_thumb_disabled_material_dark = 0x7f050055
com.example.tikoppij4:color/secondary_text_disabled_material_dark = 0x7f050053
com.example.tikoppij4:color/secondary_text_default_material_light = 0x7f050052
com.example.tikoppij4:color/secondary_text_default_material_dark = 0x7f050051
com.example.tikoppij4:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0b0005
com.example.tikoppij4:attr/actionMenuTextColor = 0x7f030010
com.example.tikoppij4:color/primary_text_disabled_material_dark = 0x7f05004a
com.example.tikoppij4:color/ripple_material_dark = 0x7f05004f
com.example.tikoppij4:style/Theme.AppCompat.CompactMenu = 0x7f0f00fa
com.example.tikoppij4:dimen/hint_pressed_alpha_material_dark = 0x7f06005f
com.example.tikoppij4:color/purple_700 = 0x7f05004e
com.example.tikoppij4:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
com.example.tikoppij4:color/purple_500 = 0x7f05004d
com.example.tikoppij4:styleable/ActivityChooserView = 0x7f100005
com.example.tikoppij4:color/purple_200 = 0x7f05004c
com.example.tikoppij4:id/useLogo = 0x7f0900b7
com.example.tikoppij4:id/accessibility_custom_action_12 = 0x7f09000b
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0f00e0
com.example.tikoppij4:color/primary_text_disabled_material_light = 0x7f05004b
com.example.tikoppij4:dimen/abc_dialog_corner_radius_material = 0x7f06001b
com.example.tikoppij4:id/home = 0x7f09005d
com.example.tikoppij4:color/primary_text_default_material_dark = 0x7f050048
com.example.tikoppij4:dimen/tooltip_precise_anchor_threshold = 0x7f060074
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f0f0026
com.example.tikoppij4:id/radio = 0x7f09007b
com.example.tikoppij4:color/primary_material_light = 0x7f050047
com.example.tikoppij4:id/icon_group = 0x7f090060
com.example.tikoppij4:string/call_notification_incoming_text = 0x7f0e0025
com.example.tikoppij4:layout/abc_action_mode_bar = 0x7f0c0004
com.example.tikoppij4:drawable/ic_visibility_off = 0x7f070072
com.example.tikoppij4:dimen/abc_dialog_fixed_width_major = 0x7f06001e
com.example.tikoppij4:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f0f0070
com.example.tikoppij4:string/abc_capital_off = 0x7f0e0006
com.example.tikoppij4:color/primary_dark_material_dark = 0x7f050044
com.example.tikoppij4:color/material_grey_900 = 0x7f050041
com.example.tikoppij4:attr/actionBarTheme = 0x7f03000a
com.example.tikoppij4:color/material_grey_50 = 0x7f05003d
com.example.tikoppij4:drawable/abc_list_selector_background_transition_holo_dark = 0x7f07002b
com.example.tikoppij4:styleable/LinearLayoutCompat_Layout = 0x7f10001e
com.example.tikoppij4:color/notification_action_color_filter = 0x7f050042
com.example.tikoppij4:dimen/abc_list_item_height_small_material = 0x7f060032
com.example.tikoppij4:color/material_grey_300 = 0x7f05003c
com.example.tikoppij4:color/material_deep_teal_500 = 0x7f05003a
com.example.tikoppij4:id/customPanel = 0x7f09004d
com.example.tikoppij4:attr/emojiCompatEnabled = 0x7f030083
com.example.tikoppij4:id/fragment_container_view_tag = 0x7f090058
com.example.tikoppij4:color/material_blue_grey_950 = 0x7f050038
com.example.tikoppij4:drawable/ic_arrow_forward = 0x7f070059
com.example.tikoppij4:attr/actionBarStyle = 0x7f030006
com.example.tikoppij4:attr/windowFixedWidthMinor = 0x7f030134
com.example.tikoppij4:drawable/abc_cab_background_internal_bg = 0x7f070010
com.example.tikoppij4:dimen/notification_subtext_size = 0x7f06006d
com.example.tikoppij4:color/material_blue_grey_900 = 0x7f050037
com.example.tikoppij4:attr/textAppearanceSearchResultTitle = 0x7f030109
com.example.tikoppij4:color/abc_decor_view_status_guard = 0x7f050005
com.example.tikoppij4:layout/notification_action = 0x7f0c001f
com.example.tikoppij4:id/list_item = 0x7f09006a
com.example.tikoppij4:attr/logoDescription = 0x7f0300ba
com.example.tikoppij4:style/Base.AlertDialog.AppCompat = 0x7f0f0005
com.example.tikoppij4:string/abc_search_hint = 0x7f0e0012
com.example.tikoppij4:color/foreground_material_light = 0x7f050033
com.example.tikoppij4:color/error_color_material_light = 0x7f050031
com.example.tikoppij4:color/dim_foreground_material_dark = 0x7f05002e
com.example.tikoppij4:id/off = 0x7f090075
com.example.tikoppij4:color/call_notification_decline_color = 0x7f05002b
com.example.tikoppij4:id/action_bar_root = 0x7f09002a
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0f00e3
com.example.tikoppij4:attr/exitAnim = 0x7f030085
com.example.tikoppij4:color/button_material_light = 0x7f050029
com.example.tikoppij4:string/dropdown_menu = 0x7f0e0031
com.example.tikoppij4:string/display_mode_fit = 0x7f0e002d
com.example.tikoppij4:id/decor_content_parent = 0x7f09004e
com.example.tikoppij4:color/bright_foreground_material_light = 0x7f050027
com.example.tikoppij4:style/Base.Widget.AppCompat.SearchView = 0x7f0f0097
com.example.tikoppij4:color/bright_foreground_inverse_material_dark = 0x7f050024
com.example.tikoppij4:attr/actionOverflowButtonStyle = 0x7f030020
com.example.tikoppij4:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0f0086
com.example.tikoppij4:color/abc_tint_edittext = 0x7f050015
com.example.tikoppij4:drawable/abc_cab_background_top_material = 0x7f070011
com.example.tikoppij4:id/action_menu_presenter = 0x7f090033
com.example.tikoppij4:color/background_material_light = 0x7f050020
com.example.tikoppij4:string/m3c_bottom_sheet_dismiss_description = 0x7f0e003f
com.example.tikoppij4:drawable/ic_visibility = 0x7f070071
com.example.tikoppij4:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
com.example.tikoppij4:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
com.example.tikoppij4:string/toggle_bottom_nav_bar = 0x7f0e0089
com.example.tikoppij4:attr/windowFixedWidthMajor = 0x7f030133
com.example.tikoppij4:id/add = 0x7f09003a
com.example.tikoppij4:styleable/TextAppearance = 0x7f100032
com.example.tikoppij4:attr/divider = 0x7f03006f
com.example.tikoppij4:style/Widget.AppCompat.Spinner = 0x7f0f0159
com.example.tikoppij4:color/androidx_core_ripple_material_light = 0x7f05001b
com.example.tikoppij4:dimen/abc_alert_dialog_button_dimen = 0x7f060011
com.example.tikoppij4:attr/textAppearanceSmallPopupMenu = 0x7f03010a
com.example.tikoppij4:attr/listPreferredItemPaddingStart = 0x7f0300b8
com.example.tikoppij4:color/abc_tint_spinner = 0x7f050017
com.example.tikoppij4:styleable/AnimatedStateListDrawableItem = 0x7f100009
com.example.tikoppij4:layout/abc_activity_chooser_view = 0x7f0c0006
com.example.tikoppij4:color/abc_tint_seek_thumb = 0x7f050016
com.example.tikoppij4:attr/dialogCornerRadius = 0x7f03006b
com.example.tikoppij4:id/action_text = 0x7f090037
com.example.tikoppij4:attr/buttonTintMode = 0x7f030048
com.example.tikoppij4:color/abc_secondary_text_material_light = 0x7f050012
com.example.tikoppij4:color/abc_search_url_text_selected = 0x7f050010
com.example.tikoppij4:color/abc_search_url_text_normal = 0x7f05000e
com.example.tikoppij4:dimen/compat_button_padding_horizontal_material = 0x7f060053
com.example.tikoppij4:attr/multiChoiceItemLayout = 0x7f0300bf
com.example.tikoppij4:styleable/FontFamily = 0x7f100017
com.example.tikoppij4:color/abc_primary_text_material_dark = 0x7f05000b
com.example.tikoppij4:style/TextAppearance.AppCompat = 0x7f0f00c1
com.example.tikoppij4:color/abc_background_cache_hint_selector_material_light = 0x7f050001
com.example.tikoppij4:attr/buttonStyle = 0x7f030045
com.example.tikoppij4:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
com.example.tikoppij4:attr/windowNoTitle = 0x7f030137
com.example.tikoppij4:string/call_notification_decline_action = 0x7f0e0023
com.example.tikoppij4:attr/goIcon = 0x7f030096
com.example.tikoppij4:dimen/hint_alpha_material_light = 0x7f06005e
com.example.tikoppij4:style/Base.Widget.AppCompat.ActionBar = 0x7f0f0067
com.example.tikoppij4:attr/actionOverflowMenuStyle = 0x7f030021
com.example.tikoppij4:attr/textAppearanceLargePopupMenu = 0x7f030103
com.example.tikoppij4:attr/actionBarSize = 0x7f030004
com.example.tikoppij4:id/line1 = 0x7f090067
com.example.tikoppij4:attr/windowActionBarOverlay = 0x7f03012f
com.example.tikoppij4:string/call_notification_hang_up_action = 0x7f0e0024
com.example.tikoppij4:attr/trackTint = 0x7f030128
com.example.tikoppij4:attr/tooltipText = 0x7f030126
com.example.tikoppij4:color/highlighted_text_material_light = 0x7f050035
com.example.tikoppij4:attr/colorBackgroundFloating = 0x7f030054
com.example.tikoppij4:string/abc_shareactionprovider_share_with = 0x7f0e0018
com.example.tikoppij4:attr/titleTextStyle = 0x7f030121
com.example.tikoppij4:styleable/AppCompatTextView = 0x7f10000f
com.example.tikoppij4:id/accessibility_custom_action_14 = 0x7f09000d
com.example.tikoppij4:dimen/compat_button_inset_horizontal_material = 0x7f060051
com.example.tikoppij4:attr/dividerPadding = 0x7f030071
com.example.tikoppij4:attr/titleMargins = 0x7f03011e
com.example.tikoppij4:attr/colorError = 0x7f030059
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f0f0022
com.example.tikoppij4:attr/titleMarginTop = 0x7f03011d
com.example.tikoppij4:string/m3c_time_picker_period_toggle_description = 0x7f0e0073
com.example.tikoppij4:drawable/abc_btn_radio_material_anim = 0x7f07000b
com.example.tikoppij4:animator/fragment_close_exit = 0x7f020001
com.example.tikoppij4:attr/alphabeticModifiers = 0x7f03002b
com.example.tikoppij4:color/background_floating_material_dark = 0x7f05001d
com.example.tikoppij4:dimen/abc_config_prefDialogWidth = 0x7f060017
com.example.tikoppij4:attr/titleMarginStart = 0x7f03011c
com.example.tikoppij4:attr/titleMargin = 0x7f030119
com.example.tikoppij4:string/m3c_search_bar_search = 0x7f0e0066
com.example.tikoppij4:attr/tint = 0x7f030116
com.example.tikoppij4:string/m3c_time_picker_hour_selection = 0x7f0e006c
com.example.tikoppij4:attr/thumbTint = 0x7f030111
com.example.tikoppij4:color/material_deep_teal_200 = 0x7f050039
com.example.tikoppij4:attr/textColorSearchUrl = 0x7f03010c
com.example.tikoppij4:dimen/abc_action_bar_content_inset_material = 0x7f060000
com.example.tikoppij4:attr/actionModeWebSearchDrawable = 0x7f03001f
com.example.tikoppij4:attr/textColorAlertDialogListItem = 0x7f03010b
com.example.tikoppij4:dimen/abc_text_size_menu_header_material = 0x7f06004a
com.example.tikoppij4:dimen/abc_button_inset_vertical_material = 0x7f060013
com.example.tikoppij4:string/abc_menu_enter_shortcut_label = 0x7f0e000b
com.example.tikoppij4:attr/textAppearanceListItemSmall = 0x7f030106
com.example.tikoppij4:attr/contentInsetEndWithActions = 0x7f030060
com.example.tikoppij4:attr/textAllCaps = 0x7f030102
com.example.tikoppij4:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0b0004
com.example.tikoppij4:color/secondary_text_disabled_material_light = 0x7f050054
com.example.tikoppij4:attr/actionModeCloseDrawable = 0x7f030014
com.example.tikoppij4:attr/targetPackage = 0x7f030101
com.example.tikoppij4:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f0f00b3
com.example.tikoppij4:attr/actionMenuTextAppearance = 0x7f03000f
com.example.tikoppij4:id/expanded_menu = 0x7f090056
com.example.tikoppij4:style/Base.Widget.AppCompat.ListView.Menu = 0x7f0f008e
com.example.tikoppij4:attr/viewInflaterClass = 0x7f03012c
com.example.tikoppij4:id/accessibility_action_clickable_span = 0x7f090006
com.example.tikoppij4:animator/fragment_fade_exit = 0x7f020003
com.example.tikoppij4:attr/imageButtonStyle = 0x7f0300a0
com.example.tikoppij4:attr/switchStyle = 0x7f0300ff
com.example.tikoppij4:style/Widget.AppCompat.ListMenuView = 0x7f0f0148
com.example.tikoppij4:string/exo_download_paused_for_wifi = 0x7f0e0039
com.example.tikoppij4:drawable/abc_list_divider_mtrl_alpha = 0x7f070026
com.example.tikoppij4:attr/suggestionRowLayout = 0x7f0300fc
com.example.tikoppij4:attr/subtitleTextStyle = 0x7f0300fb
com.example.tikoppij4:attr/argType = 0x7f03002c
com.example.tikoppij4:id/async = 0x7f09003e
com.example.tikoppij4:drawable/ic_share = 0x7f07006f
com.example.tikoppij4:attr/listPreferredItemHeightSmall = 0x7f0300b4
com.example.tikoppij4:attr/submitBackground = 0x7f0300f7
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0f0029
com.example.tikoppij4:dimen/tooltip_corner_radius = 0x7f060070
com.example.tikoppij4:attr/subtitle = 0x7f0300f8
com.example.tikoppij4:attr/spinnerStyle = 0x7f0300f1
com.example.tikoppij4:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0f00f6
com.example.tikoppij4:color/vector_tint_color = 0x7f05005f
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0f0028
com.example.tikoppij4:color/abc_tint_default = 0x7f050014
com.example.tikoppij4:style/Platform.AppCompat = 0x7f0f00a6
com.example.tikoppij4:attr/spinBars = 0x7f0300ef
com.example.tikoppij4:attr/homeAsUpIndicator = 0x7f03009a
com.example.tikoppij4:attr/titleMarginBottom = 0x7f03011a
com.example.tikoppij4:style/Theme.AppCompat.Dialog = 0x7f0f0102
com.example.tikoppij4:attr/showText = 0x7f0300ec
com.example.tikoppij4:string/m3c_date_picker_switch_to_previous_month = 0x7f0e0055
com.example.tikoppij4:id/SYM = 0x7f090005
com.example.tikoppij4:drawable/ic_favorite_border = 0x7f070063
com.example.tikoppij4:color/abc_color_highlight_material = 0x7f050004
com.example.tikoppij4:style/Widget.AppCompat.Light.PopupMenu = 0x7f0f0144
com.example.tikoppij4:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
com.example.tikoppij4:attr/showDividers = 0x7f0300eb
com.example.tikoppij4:attr/popUpTo = 0x7f0300d1
com.example.tikoppij4:attr/shortcutMatchRequired = 0x7f0300e9
com.example.tikoppij4:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f0f00d6
com.example.tikoppij4:anim/abc_popup_enter = 0x7f010003
com.example.tikoppij4:drawable/notification_bg_low = 0x7f070075
com.example.tikoppij4:attr/selectableItemBackgroundBorderless = 0x7f0300e8
com.example.tikoppij4:attr/selectableItemBackground = 0x7f0300e7
com.example.tikoppij4:attr/queryPatterns = 0x7f0300dc
com.example.tikoppij4:attr/ttcIndex = 0x7f03012a
com.example.tikoppij4:drawable/ic_launcher_background = 0x7f070067
com.example.tikoppij4:attr/seekBarStyle = 0x7f0300e6
com.example.tikoppij4:attr/alpha = 0x7f03002a
com.example.tikoppij4:style/TextAppearance.AppCompat.Title = 0x7f0f00db
com.example.tikoppij4:attr/searchViewStyle = 0x7f0300e5
com.example.tikoppij4:style/Base.V28.Theme.AppCompat = 0x7f0f005d
com.example.tikoppij4:attr/dropDownListViewStyle = 0x7f03007d
com.example.tikoppij4:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
com.example.tikoppij4:drawable/abc_ratingbar_material = 0x7f070034
com.example.tikoppij4:id/src_over = 0x7f090097
com.example.tikoppij4:attr/toolbarStyle = 0x7f030123
com.example.tikoppij4:attr/switchPadding = 0x7f0300fe
com.example.tikoppij4:attr/searchIcon = 0x7f0300e4
com.example.tikoppij4:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f0f0151
com.example.tikoppij4:style/Widget.AppCompat.ActionBar = 0x7f0f0118
com.example.tikoppij4:attr/searchHintIcon = 0x7f0300e3
com.example.tikoppij4:id/hide_ime_id = 0x7f09005b
com.example.tikoppij4:attr/textAppearanceListItem = 0x7f030104
com.example.tikoppij4:style/ThemeOverlay.AppCompat.Light = 0x7f0f0117
com.example.tikoppij4:style/Base.V7.Theme.AppCompat = 0x7f0f005f
com.example.tikoppij4:attr/thumbTextPadding = 0x7f030110
com.example.tikoppij4:attr/menu = 0x7f0300bd
com.example.tikoppij4:attr/textAppearancePopupMenuHeader = 0x7f030107
com.example.tikoppij4:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f070051
com.example.tikoppij4:string/m3c_time_picker_am = 0x7f0e0069
com.example.tikoppij4:attr/ratingBarStyle = 0x7f0300de
com.example.tikoppij4:attr/queryHint = 0x7f0300db
com.example.tikoppij4:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f0f0054
com.example.tikoppij4:attr/subtitleTextAppearance = 0x7f0300f9
com.example.tikoppij4:id/accessibility_custom_action_28 = 0x7f09001c
com.example.tikoppij4:attr/titleMarginEnd = 0x7f03011b
com.example.tikoppij4:attr/queryBackground = 0x7f0300da
com.example.tikoppij4:drawable/abc_list_selector_background_transition_holo_light = 0x7f07002c
com.example.tikoppij4:attr/progressBarPadding = 0x7f0300d8
com.example.tikoppij4:attr/popupWindowStyle = 0x7f0300d6
com.example.tikoppij4:string/state_on = 0x7f0e0084
com.example.tikoppij4:attr/popUpToSaveState = 0x7f0300d3
com.example.tikoppij4:color/abc_search_url_text_pressed = 0x7f05000f
com.example.tikoppij4:attr/windowMinWidthMajor = 0x7f030135
com.example.tikoppij4:attr/panelMenuListTheme = 0x7f0300cd
com.example.tikoppij4:drawable/abc_list_pressed_holo_dark = 0x7f070029
com.example.tikoppij4:attr/colorSwitchThumbNormal = 0x7f03005c
com.example.tikoppij4:attr/paddingEnd = 0x7f0300c9
com.example.tikoppij4:bool/abc_action_bar_embed_tabs = 0x7f040000
com.example.tikoppij4:attr/overlapAnchor = 0x7f0300c7
com.example.tikoppij4:color/background_floating_material_light = 0x7f05001e
com.example.tikoppij4:attr/numericModifiers = 0x7f0300c6
com.example.tikoppij4:color/material_grey_600 = 0x7f05003e
com.example.tikoppij4:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f0f00cf
com.example.tikoppij4:attr/actionModeSelectAllDrawable = 0x7f03001a
com.example.tikoppij4:attr/enterAnim = 0x7f030084
com.example.tikoppij4:id/tag_unhandled_key_event_manager = 0x7f0900a7
com.example.tikoppij4:attr/backgroundStacked = 0x7f030037
com.example.tikoppij4:attr/nullable = 0x7f0300c5
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0f00e8
com.example.tikoppij4:id/CTRL = 0x7f090001
com.example.tikoppij4:attr/drawableStartCompat = 0x7f030078
com.example.tikoppij4:drawable/abc_list_selector_disabled_holo_light = 0x7f07002e
com.example.tikoppij4:attr/preserveIconSpacing = 0x7f0300d7
com.example.tikoppij4:id/accessibility_custom_action_22 = 0x7f090016
com.example.tikoppij4:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f0f0044
com.example.tikoppij4:attr/state_above_anchor = 0x7f0300f5
com.example.tikoppij4:dimen/tooltip_vertical_padding = 0x7f060075
com.example.tikoppij4:attr/hideOnContentScroll = 0x7f030099
com.example.tikoppij4:attr/nestedScrollViewStyle = 0x7f0300c4
com.example.tikoppij4:color/vector_tint_theme_color = 0x7f050060
com.example.tikoppij4:attr/listPreferredItemPaddingLeft = 0x7f0300b6
com.example.tikoppij4:attr/theme = 0x7f03010e
com.example.tikoppij4:string/abc_activitychooserview_choose_application = 0x7f0e0005
com.example.tikoppij4:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
com.example.tikoppij4:attr/listPreferredItemHeightLarge = 0x7f0300b3
com.example.tikoppij4:string/m3c_time_picker_minute_selection = 0x7f0e0070
com.example.tikoppij4:string/auto_play_next_setting = 0x7f0e001f
com.example.tikoppij4:string/abc_capital_on = 0x7f0e0007
com.example.tikoppij4:attr/popEnterAnim = 0x7f0300cf
com.example.tikoppij4:attr/listPopupWindowStyle = 0x7f0300b1
com.example.tikoppij4:id/accessibility_custom_action_21 = 0x7f090015
com.example.tikoppij4:styleable/GradientColor = 0x7f10001b
com.example.tikoppij4:color/abc_btn_colored_borderless_text_material = 0x7f050002
com.example.tikoppij4:layout/abc_cascading_menu_item_layout = 0x7f0c000b
com.example.tikoppij4:color/abc_search_url_text = 0x7f05000d
com.example.tikoppij4:attr/listItemLayout = 0x7f0300ae
com.example.tikoppij4:attr/listChoiceBackgroundIndicator = 0x7f0300aa
com.example.tikoppij4:drawable/abc_ic_clear_material = 0x7f070018
com.example.tikoppij4:attr/textAppearanceListItemSecondary = 0x7f030105
com.example.tikoppij4:attr/launchSingleTop = 0x7f0300a7
com.example.tikoppij4:attr/navigationIcon = 0x7f0300c2
com.example.tikoppij4:id/topPanel = 0x7f0900b3
com.example.tikoppij4:dimen/notification_action_text_size = 0x7f060062
com.example.tikoppij4:attr/lastBaselineToBottomHeight = 0x7f0300a6
com.example.tikoppij4:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0f0112
com.example.tikoppij4:style/FloatingDialogTheme = 0x7f0f00a4
com.example.tikoppij4:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f0f006d
com.example.tikoppij4:attr/lStar = 0x7f0300a5
com.example.tikoppij4:attr/itemPadding = 0x7f0300a4
com.example.tikoppij4:drawable/abc_scrubber_track_mtrl_alpha = 0x7f07003a
com.example.tikoppij4:color/material_blue_grey_800 = 0x7f050036
com.example.tikoppij4:attr/buttonStyleSmall = 0x7f030046
com.example.tikoppij4:attr/initialActivityCount = 0x7f0300a2
com.example.tikoppij4:color/primary_text_default_material_light = 0x7f050049
com.example.tikoppij4:anim/abc_popup_exit = 0x7f010004
com.example.tikoppij4:drawable/abc_ic_menu_overflow_material = 0x7f07001d
com.example.tikoppij4:attr/indeterminateProgressStyle = 0x7f0300a1
com.example.tikoppij4:color/abc_primary_text_material_light = 0x7f05000c
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Small = 0x7f0f0021
com.example.tikoppij4:id/tag_screen_reader_focusable = 0x7f0900a3
com.example.tikoppij4:drawable/abc_ic_search_api_material = 0x7f070021
com.example.tikoppij4:string/navigation_menu = 0x7f0e0078
com.example.tikoppij4:attr/iconifiedByDefault = 0x7f03009f
com.example.tikoppij4:style/TextAppearance.AppCompat.Inverse = 0x7f0f00cb
com.example.tikoppij4:attr/spinnerDropDownItemStyle = 0x7f0300f0
com.example.tikoppij4:style/Widget.AppCompat.SearchView.ActionBar = 0x7f0f0156
com.example.tikoppij4:id/right_side = 0x7f09007e
com.example.tikoppij4:attr/closeItemLayout = 0x7f03004f
com.example.tikoppij4:attr/checkMarkTint = 0x7f03004a
com.example.tikoppij4:style/Base.Widget.AppCompat.EditText = 0x7f0f0080
com.example.tikoppij4:string/m3c_tooltip_pane_description = 0x7f0e0076
com.example.tikoppij4:dimen/abc_text_size_display_4_material = 0x7f060046
com.example.tikoppij4:style/Theme.AppCompat = 0x7f0f00f9
com.example.tikoppij4:attr/graph = 0x7f030097
com.example.tikoppij4:attr/drawableTopCompat = 0x7f03007b
com.example.tikoppij4:color/switch_thumb_material_light = 0x7f050058
com.example.tikoppij4:attr/iconTint = 0x7f03009d
com.example.tikoppij4:attr/voiceIcon = 0x7f03012d
com.example.tikoppij4:style/Widget.AppCompat.SeekBar = 0x7f0f0157
com.example.tikoppij4:animator/fragment_fade_enter = 0x7f020002
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0f00ec
com.example.tikoppij4:string/m3c_date_picker_no_selection_description = 0x7f0e004e
com.example.tikoppij4:attr/gapBetweenBars = 0x7f030095
com.example.tikoppij4:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f0f00bf
com.example.tikoppij4:id/homeAsUp = 0x7f09005e
com.example.tikoppij4:style/Widget.AppCompat.SearchView = 0x7f0f0155
com.example.tikoppij4:attr/fontStyle = 0x7f030092
com.example.tikoppij4:id/showHome = 0x7f090090
com.example.tikoppij4:anim/abc_slide_in_top = 0x7f010007
com.example.tikoppij4:style/Widget.AppCompat.SeekBar.Discrete = 0x7f0f0158
com.example.tikoppij4:attr/fontProviderSystemFontFamily = 0x7f030091
com.example.tikoppij4:attr/tickMarkTint = 0x7f030114
com.example.tikoppij4:style/Base.Widget.AppCompat.TextView = 0x7f0f009d
com.example.tikoppij4:attr/actionModeCutDrawable = 0x7f030016
com.example.tikoppij4:attr/displayOptions = 0x7f03006e
com.example.tikoppij4:string/in_progress = 0x7f0e003c
com.example.tikoppij4:attr/fontProviderPackage = 0x7f03008f
com.example.tikoppij4:attr/windowActionBar = 0x7f03012e
com.example.tikoppij4:color/switch_thumb_disabled_material_light = 0x7f050056
com.example.tikoppij4:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.example.tikoppij4:style/TextAppearance.Compat.Notification.Time = 0x7f0f00f4
com.example.tikoppij4:style/Platform.V25.AppCompat.Light = 0x7f0f00ae
com.example.tikoppij4:string/m3c_tooltip_long_press_label = 0x7f0e0075
com.example.tikoppij4:attr/iconTintMode = 0x7f03009e
com.example.tikoppij4:attr/fontProviderFallbackQuery = 0x7f03008c
com.example.tikoppij4:style/Widget.AppCompat.RatingBar.Small = 0x7f0f0154
com.example.tikoppij4:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f0f0047
com.example.tikoppij4:attr/toolbarNavigationButtonStyle = 0x7f030122
com.example.tikoppij4:style/Base.V7.Widget.AppCompat.EditText = 0x7f0f0065
com.example.tikoppij4:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.example.tikoppij4:style/Base.Widget.AppCompat.Button = 0x7f0f0072
com.example.tikoppij4:id/accessibility_custom_action_9 = 0x7f090026
com.example.tikoppij4:attr/fontProviderCerts = 0x7f03008b
com.example.tikoppij4:attr/actionBarTabStyle = 0x7f030008
com.example.tikoppij4:attr/expandActivityOverflowButtonDrawable = 0x7f030086
com.example.tikoppij4:attr/height = 0x7f030098
com.example.tikoppij4:attr/editTextColor = 0x7f030080
com.example.tikoppij4:attr/editTextBackground = 0x7f03007f
com.example.tikoppij4:string/m3c_time_picker_minute_suffix = 0x7f0e0071
com.example.tikoppij4:string/exo_download_removing = 0x7f0e003a
com.example.tikoppij4:attr/drawerArrowStyle = 0x7f03007c
com.example.tikoppij4:drawable/abc_textfield_default_mtrl_alpha = 0x7f07004b
com.example.tikoppij4:attr/autoSizeMinTextSize = 0x7f030031
com.example.tikoppij4:attr/drawableEndCompat = 0x7f030074
com.example.tikoppij4:drawable/abc_list_divider_material = 0x7f070025
com.example.tikoppij4:color/abc_hint_foreground_material_dark = 0x7f050007
com.example.tikoppij4:id/contentPanel = 0x7f09004b
com.example.tikoppij4:attr/buttonBarNegativeButtonStyle = 0x7f03003d
com.example.tikoppij4:attr/drawableBottomCompat = 0x7f030073
com.example.tikoppij4:attr/radioButtonStyle = 0x7f0300dd
com.example.tikoppij4:string/m3c_dropdown_menu_expanded = 0x7f0e0064
com.example.tikoppij4:attr/actionBarTabTextStyle = 0x7f030009
com.example.tikoppij4:attr/showTitle = 0x7f0300ed
com.example.tikoppij4:style/Widget.AppCompat.ButtonBar = 0x7f0f0129
com.example.tikoppij4:color/call_notification_answer_color = 0x7f05002a
com.example.tikoppij4:dimen/abc_dialog_fixed_height_major = 0x7f06001c
com.example.tikoppij4:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f0f00da
com.example.tikoppij4:attr/dividerHorizontal = 0x7f030070
com.example.tikoppij4:attr/dialogTheme = 0x7f03006d
com.example.tikoppij4:dimen/notification_main_column_padding_top = 0x7f060067
com.example.tikoppij4:attr/colorAccent = 0x7f030053
com.example.tikoppij4:dimen/abc_star_small = 0x7f06003d
com.example.tikoppij4:attr/panelMenuListWidth = 0x7f0300ce
com.example.tikoppij4:id/src_in = 0x7f090096
com.example.tikoppij4:attr/controlBackground = 0x7f030065
com.example.tikoppij4:styleable/NavDeepLink = 0x7f100025
com.example.tikoppij4:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f0f0068
com.example.tikoppij4:attr/contentInsetStart = 0x7f030063
com.example.tikoppij4:attr/paddingBottomNoButtons = 0x7f0300c8
com.example.tikoppij4:attr/drawableRightCompat = 0x7f030076
com.example.tikoppij4:id/checkbox = 0x7f090044
com.example.tikoppij4:attr/switchTextAppearance = 0x7f030100
com.example.tikoppij4:style/TextAppearance.AppCompat.Large = 0x7f0f00cc
com.example.tikoppij4:attr/contentInsetEnd = 0x7f03005f
com.example.tikoppij4:layout/abc_popup_menu_item_layout = 0x7f0c0013
com.example.tikoppij4:attr/drawableTint = 0x7f030079
com.example.tikoppij4:attr/actionModeFindDrawable = 0x7f030017
com.example.tikoppij4:string/play_video = 0x7f0e007b
com.example.tikoppij4:attr/thumbTintMode = 0x7f030112
com.example.tikoppij4:attr/fontProviderFetchTimeout = 0x7f03008e
com.example.tikoppij4:drawable/abc_item_background_holo_dark = 0x7f070023
com.example.tikoppij4:attr/listMenuViewStyle = 0x7f0300b0
com.example.tikoppij4:attr/colorControlNormal = 0x7f030058
com.example.tikoppij4:attr/contentInsetLeft = 0x7f030061
com.example.tikoppij4:attr/drawableLeftCompat = 0x7f030075
com.example.tikoppij4:drawable/abc_edit_text_material = 0x7f070015
com.example.tikoppij4:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f0f00b1
com.example.tikoppij4:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f0f007d
com.example.tikoppij4:attr/fontFamily = 0x7f030089
com.example.tikoppij4:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f0f014e
com.example.tikoppij4:dimen/abc_action_bar_elevation_material = 0x7f060005
com.example.tikoppij4:color/material_grey_100 = 0x7f05003b
com.example.tikoppij4:attr/collapseContentDescription = 0x7f030050
com.example.tikoppij4:attr/checkMarkTintMode = 0x7f03004b
com.example.tikoppij4:string/exo_download_description = 0x7f0e0033
com.example.tikoppij4:attr/fontProviderFetchStrategy = 0x7f03008d
com.example.tikoppij4:dimen/abc_control_inset_material = 0x7f060019
com.example.tikoppij4:color/abc_primary_text_disable_only_material_light = 0x7f05000a
com.example.tikoppij4:color/accent_material_dark = 0x7f050019
com.example.tikoppij4:dimen/abc_text_size_button_material = 0x7f060041
com.example.tikoppij4:style/Base.Theme.AppCompat = 0x7f0f003c
com.example.tikoppij4:attr/backgroundTintMode = 0x7f030039
com.example.tikoppij4:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0f0039
com.example.tikoppij4:attr/checkMarkCompat = 0x7f030049
com.example.tikoppij4:style/Base.V7.Theme.AppCompat.Dialog = 0x7f0f0060
com.example.tikoppij4:attr/fontVariationSettings = 0x7f030093
com.example.tikoppij4:attr/arrowHeadLength = 0x7f03002d
com.example.tikoppij4:drawable/abc_btn_check_material_anim = 0x7f070005
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f0f0018
com.example.tikoppij4:layout/abc_action_menu_item_layout = 0x7f0c0002
com.example.tikoppij4:attr/actionLayout = 0x7f03000e
com.example.tikoppij4:attr/srcCompat = 0x7f0300f3
com.example.tikoppij4:attr/colorControlActivated = 0x7f030056
com.example.tikoppij4:color/abc_decor_view_status_guard_light = 0x7f050006
com.example.tikoppij4:attr/actionBarPopupTheme = 0x7f030003
com.example.tikoppij4:attr/tooltipForegroundColor = 0x7f030124
com.example.tikoppij4:attr/drawableSize = 0x7f030077
com.example.tikoppij4:attr/buttonBarStyle = 0x7f030040
com.example.tikoppij4:attr/panelBackground = 0x7f0300cc
com.example.tikoppij4:style/TextAppearance.Compat.Notification.Info = 0x7f0f00f2
com.example.tikoppij4:attr/listPreferredItemPaddingRight = 0x7f0300b7
com.example.tikoppij4:attr/elevation = 0x7f030082
com.example.tikoppij4:string/search_menu_title = 0x7f0e007e
com.example.tikoppij4:color/dim_foreground_disabled_material_dark = 0x7f05002c
com.example.tikoppij4:attr/colorControlHighlight = 0x7f030057
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f0f00e4
com.example.tikoppij4:style/Base.V21.Theme.AppCompat = 0x7f0f0051
com.example.tikoppij4:attr/uri = 0x7f03012b
com.example.tikoppij4:styleable/SwitchCompat = 0x7f100031
com.example.tikoppij4:attr/navGraph = 0x7f0300c0
com.example.tikoppij4:attr/titleTextAppearance = 0x7f03011f
com.example.tikoppij4:id/search_button = 0x7f090085
com.example.tikoppij4:attr/textLocale = 0x7f03010d
com.example.tikoppij4:dimen/tooltip_precise_anchor_extra_offset = 0x7f060073
com.example.tikoppij4:xml/backup_rules = 0x7f110000
com.example.tikoppij4:attr/singleChoiceItemLayout = 0x7f0300ee
com.example.tikoppij4:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.example.tikoppij4:attr/listChoiceIndicatorSingleAnimated = 0x7f0300ac
com.example.tikoppij4:drawable/notification_action_background = 0x7f070073
com.example.tikoppij4:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0f00de
com.example.tikoppij4:style/TextAppearance.AppCompat.Large.Inverse = 0x7f0f00cd
com.example.tikoppij4:attr/buttonGravity = 0x7f030042
com.example.tikoppij4:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f0f013a
com.example.tikoppij4:layout/custom_dialog = 0x7f0c001c
com.example.tikoppij4:animator/fragment_open_exit = 0x7f020005
com.example.tikoppij4:id/activity_chooser_view_content = 0x7f090039
com.example.tikoppij4:drawable/notification_bg_normal_pressed = 0x7f070079
com.example.tikoppij4:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0b0001
com.example.tikoppij4:attr/buttonBarNeutralButtonStyle = 0x7f03003e
com.example.tikoppij4:anim/abc_slide_out_top = 0x7f010009
com.example.tikoppij4:color/bright_foreground_material_dark = 0x7f050026
com.example.tikoppij4:color/background_material_dark = 0x7f05001f
com.example.tikoppij4:string/exo_download_completed = 0x7f0e0032
com.example.tikoppij4:id/none = 0x7f090070
com.example.tikoppij4:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f070036
com.example.tikoppij4:dimen/notification_large_icon_width = 0x7f060066
com.example.tikoppij4:attr/actionBarSplitStyle = 0x7f030005
com.example.tikoppij4:id/showTitle = 0x7f090091
com.example.tikoppij4:attr/actionModeShareDrawable = 0x7f03001b
com.example.tikoppij4:attr/autoSizeTextType = 0x7f030034
com.example.tikoppij4:attr/dataPattern = 0x7f030068
com.example.tikoppij4:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.example.tikoppij4:attr/backgroundTint = 0x7f030038
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0f002b
com.example.tikoppij4:attr/alertDialogTheme = 0x7f030028
com.example.tikoppij4:style/Base.V21.Theme.AppCompat.Dialog = 0x7f0f0052
com.example.tikoppij4:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.example.tikoppij4:attr/autoSizeStepGranularity = 0x7f030033
com.example.tikoppij4:attr/lineHeight = 0x7f0300a9
com.example.tikoppij4:dimen/disabled_alpha_material_light = 0x7f060059
com.example.tikoppij4:attr/tickMarkTintMode = 0x7f030115
com.example.tikoppij4:attr/autoSizePresetSizes = 0x7f030032
com.example.tikoppij4:attr/colorButtonNormal = 0x7f030055
com.example.tikoppij4:attr/tooltipFrameBackground = 0x7f030125
com.example.tikoppij4:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
com.example.tikoppij4:string/abc_menu_alt_shortcut_label = 0x7f0e0008
com.example.tikoppij4:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
com.example.tikoppij4:attr/isLightTheme = 0x7f0300a3
com.example.tikoppij4:id/action_bar_subtitle = 0x7f09002c
com.example.tikoppij4:style/Widget.AppCompat.RatingBar = 0x7f0f0152
com.example.tikoppij4:attr/color = 0x7f030052
com.example.tikoppij4:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f0f0101
com.example.tikoppij4:dimen/abc_star_big = 0x7f06003b
com.example.tikoppij4:attr/popupMenuStyle = 0x7f0300d4
com.example.tikoppij4:style/Base.TextAppearance.AppCompat.Display4 = 0x7f0f0014
com.example.tikoppij4:attr/contentDescription = 0x7f03005e
com.example.tikoppij4:attr/popUpToInclusive = 0x7f0300d2
com.example.tikoppij4:attr/alertDialogCenterButtons = 0x7f030026
com.example.tikoppij4:attr/alertDialogStyle = 0x7f030027
com.example.tikoppij4:attr/showAsAction = 0x7f0300ea
com.example.tikoppij4:style/Animation.AppCompat.Dialog = 0x7f0f0002
com.example.tikoppij4:id/action_mode_bar_stub = 0x7f090035
com.example.tikoppij4:anim/abc_tooltip_exit = 0x7f01000b
com.example.tikoppij4:attr/actionModeSplitBackground = 0x7f03001c
com.example.tikoppij4:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f0f0141
com.example.tikoppij4:attr/fontProviderAuthority = 0x7f03008a
com.example.tikoppij4:color/material_grey_850 = 0x7f050040
com.example.tikoppij4:attr/actionModeTheme = 0x7f03001e
com.example.tikoppij4:attr/borderlessButtonStyle = 0x7f03003b
com.example.tikoppij4:string/m3c_date_picker_navigate_to_year_description = 0x7f0e004d
com.example.tikoppij4:color/abc_secondary_text_material_dark = 0x7f050011
com.example.tikoppij4:layout/abc_list_menu_item_checkbox = 0x7f0c000e
com.example.tikoppij4:id/hide_in_inspector_tag = 0x7f09005c
com.example.tikoppij4:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f0f007a
com.example.tikoppij4:attr/colorPrimary = 0x7f03005a
com.example.tikoppij4:attr/listPreferredItemHeight = 0x7f0300b2
com.example.tikoppij4:anim/abc_tooltip_enter = 0x7f01000a
com.example.tikoppij4:style/Base.Widget.AppCompat.Button.Borderless = 0x7f0f0073
com.example.tikoppij4:attr/actionModePopupWindowStyle = 0x7f030019
com.example.tikoppij4:attr/paddingStart = 0x7f0300ca
com.example.tikoppij4:attr/actionViewClass = 0x7f030023
com.example.tikoppij4:color/foreground_material_dark = 0x7f050032
com.example.tikoppij4:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f070057
com.example.tikoppij4:attr/actionDropDownStyle = 0x7f03000d
com.example.tikoppij4:dimen/notification_top_pad = 0x7f06006e
com.example.tikoppij4:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f0f00ce
com.example.tikoppij4:attr/actionModePasteDrawable = 0x7f030018
com.example.tikoppij4:style/Widget.AppCompat.ActionBar.TabBar = 0x7f0f011a
com.example.tikoppij4:attr/actionModeCloseButtonStyle = 0x7f030012
com.example.tikoppij4:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f0f00b4
com.example.tikoppij4:color/primary_dark_material_light = 0x7f050045
com.example.tikoppij4:string/m3c_time_picker_hour = 0x7f0e006a
com.example.tikoppij4:attr/defaultQueryHint = 0x7f030069
com.example.tikoppij4:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
com.example.tikoppij4:color/material_grey_800 = 0x7f05003f
com.example.tikoppij4:id/pooling_container_listener_holder_tag = 0x7f090078
com.example.tikoppij4:attr/collapseIcon = 0x7f030051
com.example.tikoppij4:id/action_divider = 0x7f090030
com.example.tikoppij4:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f0f010c
com.example.tikoppij4:string/call_notification_answer_video_action = 0x7f0e0022
com.example.tikoppij4:id/accessibility_custom_action_6 = 0x7f090023
com.example.tikoppij4:attr/popExitAnim = 0x7f0300d0
com.example.tikoppij4:anim/abc_slide_in_bottom = 0x7f010006
com.example.tikoppij4:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.example.tikoppij4:attr/firstBaselineToTopHeight = 0x7f030087
com.example.tikoppij4:attr/maxButtonHeight = 0x7f0300bb
com.example.tikoppij4:color/bright_foreground_inverse_material_light = 0x7f050025
com.example.tikoppij4:drawable/notification_template_icon_low_bg = 0x7f07007d
com.example.tikoppij4:attr/actionButtonStyle = 0x7f03000c
com.example.tikoppij4:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0f00f7
com.example.tikoppij4:color/abc_primary_text_disable_only_material_dark = 0x7f050009
com.example.tikoppij4:attr/track = 0x7f030127
com.example.tikoppij4:color/abc_btn_colored_text_material = 0x7f050003
com.example.tikoppij4:attr/alertDialogButtonGroupStyle = 0x7f030025
com.example.tikoppij4:attr/buttonCompat = 0x7f030041
com.example.tikoppij4:style/Base.Animation.AppCompat.DropDownUp = 0x7f0f0008
com.example.tikoppij4:id/accessibility_custom_action_8 = 0x7f090025
com.example.tikoppij4:attr/autoSizeMaxTextSize = 0x7f030030
com.example.tikoppij4:id/accessibility_custom_action_2 = 0x7f090013
com.example.tikoppij4:attr/actionBarDivider = 0x7f030001
com.example.tikoppij4:drawable/ic_favorite = 0x7f070062
com.example.tikoppij4:attr/homeLayout = 0x7f03009b
com.example.tikoppij4:drawable/abc_text_select_handle_right_mtrl = 0x7f070049
com.example.tikoppij4:attr/actionBarItemBackground = 0x7f030002
com.example.tikoppij4:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
com.example.tikoppij4:string/close_drawer = 0x7f0e0028
com.example.tikoppij4:anim/abc_fade_out = 0x7f010001
com.example.tikoppij4:string/abc_action_bar_up_description = 0x7f0e0001
com.example.tikoppij4:attr/windowFixedHeightMajor = 0x7f030131
com.example.tikoppij4:attr/action = 0x7f030000
com.example.tikoppij4:attr/actionModeBackground = 0x7f030011
com.example.tikoppij4:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0900ba
com.example.tikoppij4:attr/actionModeCloseContentDescription = 0x7f030013
com.example.tikoppij4:id/report_drawn = 0x7f09007c
com.example.tikoppij4:attr/thickness = 0x7f03010f
com.example.tikoppij4:id/progress_circular = 0x7f090079
com.example.tikoppij4:color/abc_tint_btn_checkable = 0x7f050013
com.example.tikoppij4:string/indeterminate = 0x7f0e003d
com.example.tikoppij4:id/tag_accessibility_clickable_spans = 0x7f09009c
com.example.tikoppij4:color/accent_material_light = 0x7f05001a
com.example.tikoppij4:styleable/FragmentContainerView = 0x7f10001a
com.example.tikoppij4:attr/trackTintMode = 0x7f030129
com.example.tikoppij4:dimen/abc_star_medium = 0x7f06003c
com.example.tikoppij4:attr/actionBarWidgetTheme = 0x7f03000b
com.example.tikoppij4:attr/checkboxStyle = 0x7f03004c
com.example.tikoppij4:style/AlertDialog.AppCompat = 0x7f0f0000
com.example.tikoppij4:string/abc_menu_sym_shortcut_label = 0x7f0e0010
com.example.tikoppij4:attr/actionModeStyle = 0x7f03001d
com.example.tikoppij4:attr/editTextStyle = 0x7f030081
com.example.tikoppij4:style/Theme.AppCompat.NoActionBar = 0x7f0f010e
com.example.tikoppij4:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.example.tikoppij4:style/TextAppearance.AppCompat.Menu = 0x7f0f00d4
com.example.tikoppij4:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.example.tikoppij4:attr/activityChooserViewStyle = 0x7f030024
com.example.tikoppij4:style/Widget.AppCompat.ListView.DropDown = 0x7f0f014b
com.example.tikoppij4:attr/windowFixedHeightMinor = 0x7f030132
com.example.tikoppij4:id/search_src_text = 0x7f09008b
com.example.tikoppij4:attr/destination = 0x7f03006a
com.example.tikoppij4:string/range_end = 0x7f0e007c
com.example.tikoppij4:attr/switchMinWidth = 0x7f0300fd
com.example.tikoppij4:dimen/compat_control_corner_material = 0x7f060055
com.example.tikoppij4:color/ripple_material_light = 0x7f050050
com.example.tikoppij4:attr/ratingBarStyleSmall = 0x7f0300e0
com.example.tikoppij4:dimen/abc_text_size_display_1_material = 0x7f060043
com.example.tikoppij4:animator/fragment_close_enter = 0x7f020000
com.example.tikoppij4:color/bright_foreground_disabled_material_dark = 0x7f050022
com.example.tikoppij4:attr/listLayout = 0x7f0300af
com.example.tikoppij4:attr/fontProviderQuery = 0x7f030090
com.example.tikoppij4:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f070055
com.example.tikoppij4:drawable/notification_template_icon_bg = 0x7f07007c
com.example.tikoppij4:dimen/notification_top_pad_large_text = 0x7f06006f
com.example.tikoppij4:attr/customNavigationLayout = 0x7f030066
com.example.tikoppij4:styleable/StateListDrawableItem = 0x7f100030
com.example.tikoppij4:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f0f009e
com.example.tikoppij4:anim/abc_fade_in = 0x7f010000
com.example.tikoppij4:string/m3c_date_picker_switch_to_input_mode = 0x7f0e0053
com.example.tikoppij4:drawable/abc_star_half_black_48dp = 0x7f070041
com.example.tikoppij4:attr/data = 0x7f030067
com.example.tikoppij4:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.example.tikoppij4:string/m3c_date_input_invalid_for_pattern = 0x7f0e0045
com.example.tikoppij4:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.example.tikoppij4:id/accessibility_custom_action_5 = 0x7f090022
com.example.tikoppij4:string/default_popup_window_title = 0x7f0e002b
com.example.tikoppij4:string/abc_toolbar_collapse_description = 0x7f0e001a
com.example.tikoppij4:color/bright_foreground_disabled_material_light = 0x7f050023
com.example.tikoppij4:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.example.tikoppij4:attr/popupTheme = 0x7f0300d5
com.example.tikoppij4:animator/fragment_open_enter = 0x7f020004
com.example.tikoppij4:id/ifRoom = 0x7f090061
com.example.tikoppij4:string/m3c_date_input_invalid_year_range = 0x7f0e0047
com.example.tikoppij4:id/action_container = 0x7f09002e
com.example.tikoppij4:attr/listChoiceIndicatorMultipleAnimated = 0x7f0300ab
com.example.tikoppij4:attr/background = 0x7f030035
com.example.tikoppij4:id/withText = 0x7f0900be
com.example.tikoppij4:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f070038
com.example.tikoppij4:dimen/abc_action_bar_stacked_max_height = 0x7f060009
com.example.tikoppij4:attr/closeIcon = 0x7f03004e
