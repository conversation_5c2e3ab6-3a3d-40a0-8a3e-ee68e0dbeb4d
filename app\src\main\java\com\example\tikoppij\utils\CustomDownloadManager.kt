package com.example.tikoppij.utils

import android.content.ContentResolver
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.concurrent.TimeUnit

/**
 * 自定义下载管理器
 * 不使用系统DownloadManager，完全自定义实现
 * 支持进度回调，文件保存到DCIM/tikapp目录
 */
object CustomDownloadManager {
    
    private val TAG = "CustomDownloadManager"
    
    // 下载状态
    data class DownloadState(
        val isDownloading: Boolean = false,
        val progress: Float = 0f,
        val downloadedBytes: Long = 0L,
        val totalBytes: Long = 0L,
        val fileName: String = "",
        val errorMessage: String? = null
    )
    
    // 下载状态流
    private val _downloadState = MutableStateFlow(DownloadState())
    val downloadState: StateFlow<DownloadState> = _downloadState
    
    // 下载用的HttpClient - 增加超时时间适应大文件下载
    private val downloadClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .build()
    
    /**
     * 下载视频文件
     */
    suspend fun downloadVideo(context: Context, videoUrl: String): DownloadResult = withContext(Dispatchers.IO) {
        try {
            // 检查是否已经在下载中
            if (_downloadState.value.isDownloading) {
                return@withContext DownloadResult.Error("已有下载任务正在进行中，请等待完成后再试")
            }
            
            Log.d(TAG, "开始下载视频: $videoUrl")
            
            // 生成随机文件名
            val finalFileName = generateRandomFileName()
            Log.d(TAG, "文件名: $finalFileName")
            
            // 重置下载状态
            _downloadState.value = DownloadState(
                isDownloading = true,
                fileName = finalFileName
            )
            
            // 创建下载请求
            val request = Request.Builder()
                .url(videoUrl)
                .build()
            
            // 执行下载
            val response = downloadClient.newCall(request).execute()
            
            if (!response.isSuccessful) {
                val error = "下载请求失败: ${response.code}"
                _downloadState.value = _downloadState.value.copy(
                    isDownloading = false,
                    errorMessage = error
                )
                return@withContext DownloadResult.Error(error)
            }
            
            val responseBody = response.body
            if (responseBody == null) {
                val error = "响应体为空"
                _downloadState.value = _downloadState.value.copy(
                    isDownloading = false,
                    errorMessage = error
                )
                return@withContext DownloadResult.Error(error)
            }
            
            val totalBytes = responseBody.contentLength()
            Log.d(TAG, "文件总大小: $totalBytes bytes")
            
            // 使用MediaStore API创建文件
            val (uri, outputStream) = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                createFileUsingMediaStore(context, finalFileName)
            } else {
                createFileUsingTraditionalMethod(finalFileName)
            }
            
            if (uri == null || outputStream == null) {
                val error = "无法创建文件"
                _downloadState.value = _downloadState.value.copy(
                    isDownloading = false,
                    errorMessage = error
                )
                return@withContext DownloadResult.Error(error)
            }
            
            // 开始写入文件
            responseBody.byteStream().use { inputStream ->
                outputStream.use { output ->
                    val buffer = ByteArray(8192) // 8KB缓冲区
                    var downloadedBytes = 0L
                    var bytesRead: Int
                    
                    while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                        // 检查是否被取消
                        if (!_downloadState.value.isDownloading) {
                            return@withContext DownloadResult.Error("下载已取消")
                        }
                        
                        output.write(buffer, 0, bytesRead)
                        downloadedBytes += bytesRead
                        
                        // 更新下载进度
                        val progress = if (totalBytes > 0) {
                            downloadedBytes.toFloat() / totalBytes.toFloat()
                        } else {
                            0f
                        }
                        
                        _downloadState.value = _downloadState.value.copy(
                            progress = progress,
                            downloadedBytes = downloadedBytes,
                            totalBytes = totalBytes
                        )
                        
                        Log.d(TAG, "下载进度: ${(progress * 100).toInt()}% ($downloadedBytes/$totalBytes)")
                    }
                }
            }
            
            Log.d(TAG, "下载完成: $uri")
            
            // 下载完成后，触发媒体扫描（如果是传统方法）
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                val filePath = getFilePathFromUri(context, uri)
                if (filePath != null) {
                    scanMediaFile(context, File(filePath))
                }
            }
            
            // 下载完成
            _downloadState.value = _downloadState.value.copy(
                isDownloading = false,
                progress = 1.0f
            )
            
            DownloadResult.Success(uri.toString(), finalFileName)
            
        } catch (e: IOException) {
            val error = "网络错误: ${e.message}"
            Log.e(TAG, error, e)
            _downloadState.value = _downloadState.value.copy(
                isDownloading = false,
                errorMessage = error
            )
            DownloadResult.Error(error)
            
        } catch (e: Exception) {
            val error = "下载异常: ${e.message}"
            Log.e(TAG, error, e)
            _downloadState.value = _downloadState.value.copy(
                isDownloading = false,
                errorMessage = error
            )
            DownloadResult.Error(error)
        }
    }
    
    /**
     * 使用MediaStore API创建文件 (Android 10+)
     */
    private fun createFileUsingMediaStore(context: Context, fileName: String): Pair<Uri?, java.io.OutputStream?> {
        return try {
            val contentValues = ContentValues().apply {
                put(MediaStore.Video.Media.DISPLAY_NAME, fileName)
                put(MediaStore.Video.Media.MIME_TYPE, "video/mp4")
                put(MediaStore.Video.Media.RELATIVE_PATH, "DCIM/tikapp")
            }
            
            val resolver = context.contentResolver
            val uri = resolver.insert(MediaStore.Video.Media.EXTERNAL_CONTENT_URI, contentValues)
            
            if (uri != null) {
                val outputStream = resolver.openOutputStream(uri)
                Log.d(TAG, "使用MediaStore创建文件: $uri")
                Pair(uri, outputStream)
            } else {
                Log.e(TAG, "MediaStore创建文件失败")
                Pair(null, null)
            }
        } catch (e: Exception) {
            Log.e(TAG, "MediaStore创建文件异常", e)
            Pair(null, null)
        }
    }
    
    /**
     * 使用传统方法创建文件 (Android 9及以下)
     */
    private fun createFileUsingTraditionalMethod(fileName: String): Pair<Uri?, java.io.OutputStream?> {
        return try {
            val saveDir = createSaveDirectory()
            if (saveDir == null) {
                return Pair(null, null)
            }
            
            val saveFile = File(saveDir, fileName)
            val uri = Uri.fromFile(saveFile)
            val outputStream = FileOutputStream(saveFile)
            
            Log.d(TAG, "使用传统方法创建文件: ${saveFile.absolutePath}")
            Pair(uri, outputStream)
        } catch (e: Exception) {
            Log.e(TAG, "传统方法创建文件异常", e)
            Pair(null, null)
        }
    }
    
    /**
     * 从Uri获取文件路径
     */
    private fun getFilePathFromUri(context: Context, uri: Uri): String? {
        return try {
            if (uri.scheme == "file") {
                uri.path
            } else {
                // 对于content://类型的Uri，无法直接获取路径
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取文件路径失败", e)
            null
        }
    }
    
    /**
     * 创建保存目录 DCIM/tikapp
     */
    private fun createSaveDirectory(): File? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ 使用MediaStore API
                val dcimDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM)
                val tikappDir = File(dcimDir, "tikapp")
                
                if (!tikappDir.exists()) {
                    val created = tikappDir.mkdirs()
                    if (!created) {
                        Log.e(TAG, "无法创建目录: ${tikappDir.absolutePath}")
                        return null
                    }
                }
                
                Log.d(TAG, "保存目录: ${tikappDir.absolutePath}")
                tikappDir
            } else {
                // Android 9及以下使用传统方式
                val dcimDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM)
                val tikappDir = File(dcimDir, "tikapp")
                
                if (!tikappDir.exists()) {
                    val created = tikappDir.mkdirs()
                    if (!created) {
                        Log.e(TAG, "无法创建目录: ${tikappDir.absolutePath}")
                        return null
                    }
                }
                
                Log.d(TAG, "保存目录: ${tikappDir.absolutePath}")
                tikappDir
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "创建保存目录失败", e)
            null
        }
    }
    
    /**
     * 触发媒体扫描，让下载的文件在相册中显示
     */
    private fun scanMediaFile(context: Context, file: File) {
        try {
            // 使用MediaScannerConnection扫描单个文件
            MediaScannerConnection.scanFile(
                context,
                arrayOf(file.absolutePath),
                arrayOf("video/mp4"),
                { path, uri ->
                    Log.d(TAG, "媒体扫描完成: $path -> $uri")
                }
            )
            
            Log.d(TAG, "已触发媒体扫描: ${file.absolutePath}")
            
        } catch (e: Exception) {
            Log.e(TAG, "媒体扫描失败", e)
        }
    }
    
    /**
     * 生成随机文件名
     * 格式：3个大写字母 + 3个小写字母 + .mp4
     */
    private fun generateRandomFileName(): String {
        val upperCaseLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        val lowerCaseLetters = "abcdefghijklmnopqrstuvwxyz"
        
        val upperPart = (1..3).map { upperCaseLetters.random() }.joinToString("")
        val lowerPart = (1..3).map { lowerCaseLetters.random() }.joinToString("")
        
        return "${upperPart}${lowerPart}.mp4"
    }
    
    /**
     * 取消当前下载
     */
    fun cancelDownload() {
        _downloadState.value = _downloadState.value.copy(
            isDownloading = false,
            errorMessage = "下载已取消"
        )
    }
    
    /**
     * 重置下载状态
     */
    fun resetDownloadState() {
        _downloadState.value = DownloadState()
    }
    
    /**
     * 下载结果密封类
     */
    sealed class DownloadResult {
        data class Success(val filePath: String, val fileName: String) : DownloadResult()
        data class Error(val message: String) : DownloadResult()
    }
} 