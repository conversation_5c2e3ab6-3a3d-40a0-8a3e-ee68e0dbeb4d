package com.example.tikoppij.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.util.UnstableApi
import com.example.tikoppij.data.FavoriteRepository
import com.example.tikoppij.data.HistoryRepository
import com.example.tikoppij.data.UserPreferencesRepository
import com.example.tikoppij.video.MediaPlayerService
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 使用 Koin 依赖注入的示例 ViewModel
 * 展示如何通过构造函数注入依赖
 * 
 * 注意：这个 ViewModel 展示了纯 Koin 的使用方式
 * 实际项目中可以根据需要选择使用这种方式或继续使用 BaseVideoViewModel
 */
@UnstableApi
class KoinExampleViewModel(
    private val favoriteRepository: FavoriteRepository,
    private val historyRepository: HistoryRepository,
    private val userPreferencesRepository: UserPreferencesRepository,
    private val mediaPlayerService: MediaPlayerService
) : ViewModel() {
    
    private val _uiState = MutableStateFlow("Koin 依赖注入示例")
    val uiState: StateFlow<String> = _uiState.asStateFlow()
    
    init {
        // 展示如何使用注入的依赖
        viewModelScope.launch {
            // 使用用户偏好仓库
            userPreferencesRepository.videoDisplayMode.collect { mode ->
                _uiState.value = "当前显示模式: $mode"
            }
        }
    }
    
    /**
     * 示例方法：展示如何使用注入的依赖
     */
    fun exampleUsage() {
        viewModelScope.launch {
            // 使用收藏仓库
            favoriteRepository.getFavoriteList().collect { favorites ->
                _uiState.value = "收藏数量: ${favorites.size}"
            }
        }
    }
}

/**
 * 如果需要在 Compose 中使用这个 ViewModel，可以这样做：
 * 
 * @Composable
 * fun ExampleScreen() {
 *     val viewModel: KoinExampleViewModel = koinViewModel()
 *     val uiState by viewModel.uiState.collectAsState()
 *     
 *     Text(text = uiState)
 * }
 */ 