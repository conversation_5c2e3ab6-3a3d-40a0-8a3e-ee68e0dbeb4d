package com.example.tikoppij

import android.app.Application
import com.example.tikoppij.di.allModules
import com.example.tikoppij.utils.PerformanceMonitor
import org.koin.android.ext.koin.androidContext
import org.koin.core.context.startKoin

/**
 * Tikoppij应用程序主类
 * 
 * 这个类是整个应用的入口点，负责全局级别的初始化工作。
 * 使用 Koin 依赖注入框架管理所有依赖关系。
 * 
 * 设计原则：
 * - 使用 Koin 进行依赖注入：统一管理所有依赖
 * - 利用App Startup：复杂的初始化工作交给专门的Initializer处理
 * - 性能监控：记录关键的性能指标
 * 
 * <AUTHOR>
 * @since 1.0
 */
class MyApplication : Application() {
    
    /**
     * 应用程序创建时的回调
     * 
     * 初始化 Koin 依赖注入框架和性能监控。
     * 其他复杂的初始化工作通过 App Startup 在后台并行处理。
     */
    override fun onCreate() {
        super.onCreate()
        
        // 初始化 Koin 依赖注入框架
        startKoin {
            androidContext(this@MyApplication)
            modules(allModules)
        }
        
        // 记录应用onCreate完成时间点
        // 用于性能分析和优化，帮助监控应用启动速度
        PerformanceMonitor.recordTimePoint("应用onCreate完成")
        
        // 注意：其他初始化工作（如网络配置、缓存初始化等）
        // 已经通过App Startup在initializer包中的AppInitializer完成
    }
} 