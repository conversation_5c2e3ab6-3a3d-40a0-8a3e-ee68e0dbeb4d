package com.example.tikoppij

import android.os.Bundle
import android.view.WindowManager
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.BackHandler
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.core.view.WindowCompat
import androidx.media3.common.util.UnstableApi
import androidx.navigation.NavHostController
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.example.tikoppij.ui.components.BottomNavBar
import com.example.tikoppij.ui.components.DefaultDrawerContent
import com.example.tikoppij.ui.components.NavigationDrawerComponent
import com.example.tikoppij.ui.navigation.AppDestinations
import com.example.tikoppij.ui.navigation.AppNavGraph
import com.example.tikoppij.ui.navigation.bottomNavItems
import com.example.tikoppij.ui.theme.AppDarkGrey
import com.example.tikoppij.ui.theme.NavBarContentColor
import com.example.tikoppij.ui.theme.TikoppijTheme
import com.example.tikoppij.ui.theme.VideoPlayerBackground
import com.example.tikoppij.utils.PerformanceMonitor
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@UnstableApi
class MainActivity : ComponentActivity() {

    private var backPressedOnce = false
    private var backPressedJob: Job? = null
    private val mainScope = CoroutineScope(Dispatchers.Main)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 启用Edge-to-Edge模式，这将允许内容绘制到系统栏后面，
        // 并将系统栏背景设置为透明，同时自动调整前景内容的颜色。
        enableEdgeToEdge()
        
        // 保持屏幕常亮
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        
        // 记录Activity创建时间
        PerformanceMonitor.recordTimePoint("MainActivity.onCreate")
        
        setContent {
            // 记录Compose setContent开始时间
            PerformanceMonitor.recordTimePoint("Compose.setContent")
            
            TikoppijTheme {
                // 监控UI渲染完成
                DisposableEffect(Unit) {
                    PerformanceMonitor.recordTimePoint("UI初始渲染")
                    onDispose { }
                }
                
                        // 创建导航控制器
        val navController = rememberNavController()
        val currentBackStack by navController.currentBackStackEntryAsState()
        val currentRoute = currentBackStack?.destination?.route
        val isHomePage = currentRoute == AppDestinations.Home.route
        
        // 底部导航栏可见性状态
        var isBottomBarVisible by remember { mutableStateOf(true) }
        var isStatusBarVisible by remember { mutableStateOf(true) }

        // 根据当前路由控制底部导航栏可见性
        LaunchedEffect(currentRoute) {
            // 在播放页面、缓存管理页面、收藏列表、历史列表页面隐藏底部导航栏
            isBottomBarVisible = when (currentRoute) {
                AppDestinations.CacheManagement.route,
                AppDestinations.FavoriteList.route,
                AppDestinations.HistoryList.route,
                AppDestinations.DownloadList.route,
                AppDestinations.FavoritePlayer.route,
                AppDestinations.HistoryPlayer.route,
                AppDestinations.DownloadPlayer.route -> false
                else -> true
            }
        }
        
        val toggleBottomBarVisibility = { 
            isBottomBarVisible = !isBottomBarVisible
            isStatusBarVisible = !isStatusBarVisible
            
            // 控制状态栏显示/隐藏
            val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
            if (isStatusBarVisible) {
                windowInsetsController.show(androidx.core.view.WindowInsetsCompat.Type.statusBars())
            } else {
                windowInsetsController.hide(androidx.core.view.WindowInsetsCompat.Type.statusBars())
            }
        }
        
        // 抽屉导航状态
        var isDrawerOpen by remember { mutableStateOf(false) }
        val toggleDrawer = { 
            isDrawerOpen = !isDrawerOpen 
        }

                // 控制状态栏图标颜色
                val systemInDarkTheme = isSystemInDarkTheme() // 获取系统主题状态
                LaunchedEffect(isHomePage, systemInDarkTheme) {
                    val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
                    if (isHomePage) {
                        windowInsetsController.isAppearanceLightStatusBars = false // 首页：强制浅色图标 (白色)
                    } else {
                        // 其他页面：根据系统主题动态调整
                        // true 表示浅色主题 (深色图标), false 表示深色主题 (浅色图标)
                        windowInsetsController.isAppearanceLightStatusBars = !systemInDarkTheme
                    }
                }
                
                // 处理返回按钮事件，实现首页"再按一次退出"
                if (isHomePage) {
                    BackHandler(enabled = true) {
                        if (backPressedOnce) {
                            backPressedJob?.cancel() // 取消定时器
                            finish() // 退出应用
                        } else {
                            backPressedOnce = true
                            Toast.makeText(this@MainActivity, "再按一次退出应用", Toast.LENGTH_SHORT).show()
                            backPressedJob = mainScope.launch {
                                delay(2000) // 2秒后重置状态
                                backPressedOnce = false
                            }
                        }
                    }
                } else {
                    // BackHandler在enabled=false时会自动恢复默认行为
                }
                
                // 使用改进的Scaffold布局
                MainScaffold(
                    navController = navController,
                    isBottomBarVisible = isBottomBarVisible,
                    isStatusBarVisible = isStatusBarVisible,
                    toggleBottomBarVisibility = toggleBottomBarVisibility,
                    isDrawerOpen = isDrawerOpen,
                    toggleDrawer = toggleDrawer
                )
            }
        }
    }
    
    override fun onResume() {
        super.onResume()
        // 记录Activity可见时间
        PerformanceMonitor.recordTimePoint("MainActivity.onResume")
    }

    override fun onDestroy() {
        super.onDestroy()
        backPressedJob?.cancel() // 清理 Coroutine
        window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON) // 清除屏幕常亮标记
    }
}

/**
 * 主Scaffold组件，根据页面类型处理不同的内边距
 */
@OptIn(ExperimentalMaterial3Api::class)
@UnstableApi
@Composable
fun MainScaffold(
    navController: NavHostController,
    isBottomBarVisible: Boolean,
    isStatusBarVisible: Boolean,
    toggleBottomBarVisibility: () -> Unit,
    isDrawerOpen: Boolean = false,
    toggleDrawer: () -> Unit = {}
) {
    val bottomNavBackgroundColor = AppDarkGrey
    val bottomNavContentColor = NavBarContentColor

    // 使用抽屉组件包裹整个内容
    NavigationDrawerComponent(
        isOpen = isDrawerOpen,
        onDismiss = toggleDrawer,
        drawerContent = {
            DefaultDrawerContent()
        },
        content = {
            Scaffold(
                modifier = Modifier.fillMaxSize(),
                containerColor = VideoPlayerBackground,
                bottomBar = {
                    AnimatedVisibility(
                        visible = isBottomBarVisible,
                        enter = fadeIn(),
                        exit = fadeOut()
                    ) {
                        // Column 依然保留，以备将来可能在 BottomNavBar 上下添加其他元素
                        Column { 
                            // 移除了 HorizontalDivider
                            BottomNavBar(
                                navController = navController,
                                items = bottomNavItems,
                                containerColor = bottomNavBackgroundColor,
                                contentColor = bottomNavContentColor
                            )
                        }
                    }
                }
            ) { innerPadding ->
                val animatedBottomPadding by animateDpAsState(
                    targetValue = innerPadding.calculateBottomPadding(),
                    label = "animatedBottomPadding"
                )

                val animatedNavGraphPadding = PaddingValues(
                    start = innerPadding.calculateLeftPadding(androidx.compose.ui.unit.LayoutDirection.Ltr),
                    end = innerPadding.calculateRightPadding(androidx.compose.ui.unit.LayoutDirection.Ltr),
                    top = innerPadding.calculateTopPadding(),
                    bottom = animatedBottomPadding
                )

                AppNavGraph(
                    navController = navController,
                    innerPadding = animatedNavGraphPadding,
                    isBottomBarVisible = isBottomBarVisible,
                    isStatusBarVisible = isStatusBarVisible,
                    toggleBottomBarVisibility = toggleBottomBarVisibility
                )
            }
        }
    )
}

