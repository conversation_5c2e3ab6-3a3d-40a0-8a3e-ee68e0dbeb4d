package com.example.tikoppij.utils

import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 时间工具类
 * 提供统一的时间格式化功能
 */
object TimeUtils {
    
    /**
     * 格式化相对时间
     * 将时间戳格式化为相对时间（如：刚刚、5分钟前、2小时前等）
     * @param timestamp 时间戳
     * @return 格式化后的相对时间字符串
     */
    fun formatRelativeTime(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        
        return when {
            diff < Constants.Time.MINUTE_IN_MILLIS -> "刚刚"
            diff < Constants.Time.HOUR_IN_MILLIS -> "${diff / Constants.Time.MINUTE_IN_MILLIS}分钟前"
            diff < Constants.Time.DAY_IN_MILLIS -> "${diff / Constants.Time.HOUR_IN_MILLIS}小时前"
            diff < Constants.Time.WEEK_IN_MILLIS -> "${diff / Constants.Time.DAY_IN_MILLIS}天前"
            diff < 30 * Constants.Time.DAY_IN_MILLIS -> "${diff / Constants.Time.WEEK_IN_MILLIS}周前"
            else -> SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date(timestamp))
        }
    }
    
    /**
     * 格式化播放进度时间
     * 将毫秒时间格式化为播放进度（如：1分30秒、45秒）
     * @param progressMs 进度毫秒数
     * @return 格式化后的进度字符串
     */
    fun formatPlaybackDuration(progressMs: Long): String {
        if (progressMs <= 0) return ""
        
        val minutes = progressMs / Constants.Time.MINUTE_IN_MILLIS
        val seconds = (progressMs % Constants.Time.MINUTE_IN_MILLIS) / Constants.Time.SECOND_IN_MILLIS
        
        return if (minutes > 0) {
            "${minutes}分${seconds}秒"
        } else {
            "${seconds}秒"
        }
    }
    
    /**
     * 格式化简短时间
     * 用于收藏时间等场景（如：12-25 14:30）
     * @param timestamp 时间戳
     * @return 格式化后的时间字符串
     */
    fun formatShortTime(timestamp: Long): String {
        val formatter = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
        return formatter.format(Date(timestamp))
    }
    
    /**
     * 格式化下载时间
     * 针对下载场景的特殊格式化（支持超过30天显示具体日期）
     * @param timestamp 时间戳
     * @return 格式化后的时间字符串
     */
    fun formatDownloadTime(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        
        return when {
            diff < Constants.Time.MINUTE_IN_MILLIS -> "刚刚"
            diff < Constants.Time.HOUR_IN_MILLIS -> "${diff / Constants.Time.MINUTE_IN_MILLIS}分钟前"
            diff < Constants.Time.DAY_IN_MILLIS -> "${diff / Constants.Time.HOUR_IN_MILLIS}小时前"
            diff < 30 * Constants.Time.DAY_IN_MILLIS -> "${diff / Constants.Time.DAY_IN_MILLIS}天前"
            else -> SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date(timestamp))
        }
    }
} 