package com.example.tikoppij.ui.screens

import android.app.Activity
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.tikoppij.R
import com.example.tikoppij.model.DownloadModel
import com.example.tikoppij.model.VideoModel
import com.example.tikoppij.viewmodel.DownloadListViewModel
import com.example.tikoppij.viewmodel.DownloadListViewModelFactory
import com.example.tikoppij.utils.TimeUtils
import kotlinx.coroutines.launch

/**
 * 下载历史页面
 * 展示所有已下载的视频
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun DownloadListScreen(
    onNavigateBack: () -> Unit,
    onNavigateToPlayer: (List<VideoModel>, Int) -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    
    // ViewModel
    val viewModel: DownloadListViewModel = viewModel(
        factory = DownloadListViewModelFactory(context)
    )
    
    // 状态
    val downloadList by viewModel.downloadList.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    var showClearDialog by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    var downloadToDelete by remember { mutableStateOf<DownloadModel?>(null) }
    var showErrorDialog by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }

    val activity = context as? Activity

    // 设置状态栏为浅色主题（黑色文字）
    LaunchedEffect(Unit) {
        if (activity != null) {
            val window = activity.window
            val controller = WindowCompat.getInsetsController(window, window.decorView)
            // 设置状态栏文字为黑色（适用于浅色背景）
            controller.isAppearanceLightStatusBars = true
            controller.show(WindowInsetsCompat.Type.statusBars())
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        text = "下载管理",
                        fontWeight = FontWeight.Bold
                    ) 
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_arrow_back),
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    if (downloadList.isNotEmpty()) {
                        IconButton(
                            onClick = { showClearDialog = true }
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_delete),
                                contentDescription = "清空下载",
                                tint = Color.Red
                            )
                        }
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            )
        }
    ) { paddingValues ->
        
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                isLoading -> {
                    // 加载中
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                
                downloadList.isEmpty() -> {
                    // 空状态
                    EmptyDownloadState()
                }
                
                else -> {
                    // 下载列表
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        item {
                            // 统计信息
                            DownloadStatsCard(downloadList = downloadList)
                        }
                        
                        items(
                            items = downloadList,
                            key = { it.videoId }
                        ) { download ->
                            DownloadItem(
                                download = download,
                                onPlay = {
                                    try {
                                        val videoList = downloadList.map { it.toVideoModel() }
                                        val index = downloadList.indexOf(download)
                                        onNavigateToPlayer(videoList, index)
                                    } catch (e: Exception) {
                                        errorMessage = "播放失败：${e.message}"
                                        showErrorDialog = true
                                    }
                                },
                                onDelete = {
                                    downloadToDelete = download
                                    showDeleteDialog = true
                                }
                            )
                        }
                        
                        item {
                            // 底部间距
                            Spacer(modifier = Modifier.height(32.dp))
                        }
                    }
                }
            }
        }
    }
    
    // 清空下载确认对话框
    if (showClearDialog) {
        AlertDialog(
            onDismissRequest = { showClearDialog = false },
            title = { 
                Text(
                    text = "清空下载历史",
                    fontWeight = FontWeight.Bold
                ) 
            },
            text = { 
                Text("确定要清空所有下载记录吗？\n\n注意：这不会删除已下载的文件，只会清除应用内的下载历史记录。")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        coroutineScope.launch {
                            try {
                                viewModel.clearDownloads()
                                showClearDialog = false
                            } catch (e: Exception) {
                                errorMessage = "清空失败：${e.message}"
                                showErrorDialog = true
                                showClearDialog = false
                            }
                        }
                    },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = Color.Red
                    )
                ) {
                    Text("确定清空")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showClearDialog = false }
                ) {
                    Text("取消")
                }
            }
        )
    }
    
    // 删除单个下载确认对话框
    if (showDeleteDialog && downloadToDelete != null) {
        AlertDialog(
            onDismissRequest = { 
                showDeleteDialog = false
                downloadToDelete = null
            },
            title = { 
                Text(
                    text = "删除下载记录",
                    fontWeight = FontWeight.Bold
                ) 
            },
            text = { 
                Text("确定要删除这个下载记录吗？\n\n注意：这不会删除已下载的文件，只会从应用内的下载历史中移除。")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        downloadToDelete?.let { download ->
                            coroutineScope.launch {
                                try {
                                    viewModel.removeDownload(download.videoId)
                                    showDeleteDialog = false
                                    downloadToDelete = null
                                } catch (e: Exception) {
                                    errorMessage = "删除失败：${e.message}"
                                    showErrorDialog = true
                                    showDeleteDialog = false
                                    downloadToDelete = null
                                }
                            }
                        }
                    },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = Color.Red
                    )
                ) {
                    Text("确定删除")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { 
                        showDeleteDialog = false
                        downloadToDelete = null
                    }
                ) {
                    Text("取消")
                }
            }
        )
    }
    
    // 错误对话框
    if (showErrorDialog) {
        AlertDialog(
            onDismissRequest = { 
                showErrorDialog = false
                errorMessage = ""
            },
            title = { 
                Text(
                    text = "操作失败",
                    fontWeight = FontWeight.Bold,
                    color = Color.Red
                ) 
            },
            text = { 
                Text(errorMessage)
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showErrorDialog = false
                        errorMessage = ""
                    }
                ) {
                    Text("确定")
                }
            }
        )
    }
}

/**
 * 空状态组件
 */
@Composable
private fun EmptyDownloadState() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_download),
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = Color.Gray
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "暂无下载",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Gray
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "点击视频页面的下载按钮\n即可将视频保存到本地",
                fontSize = 14.sp,
                color = Color.Gray,
                textAlign = TextAlign.Center,
                lineHeight = 20.sp
            )
        }
    }
}

/**
 * 下载统计卡片
 */
@Composable
private fun DownloadStatsCard(
    downloadList: List<DownloadModel>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceAround
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "${downloadList.size}",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = "已下载",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }
            
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                val totalSize = downloadList.size * 15 // 估算每个视频15MB
                Text(
                    text = "${totalSize}MB",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = "总大小(估算)",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }
        }
    }
}

/**
 * 下载项目组件
 */
@Composable
private fun DownloadItem(
    download: DownloadModel,
    onPlay: () -> Unit,
    onDelete: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onPlay() },
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 第一行：文件名和操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = download.fileName,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                
                Row {
                    // 播放按钮
                    IconButton(
                        onClick = onPlay,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_play_arrow),
                            contentDescription = "播放",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                    
                    // 删除按钮
                    IconButton(
                        onClick = onDelete,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_delete),
                            contentDescription = "删除",
                            tint = Color.Red,
                            modifier = Modifier.size(18.dp)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 第二行：视频信息
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "${download.width}x${download.height}",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    modifier = Modifier
                        .background(
                            Color.Gray.copy(alpha = 0.1f),
                            RoundedCornerShape(4.dp)
                        )
                        .padding(horizontal = 6.dp, vertical = 2.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "分类${download.category}",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    modifier = Modifier
                        .background(
                            MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f),
                            RoundedCornerShape(4.dp)
                        )
                        .padding(horizontal = 6.dp, vertical = 2.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 第三行：下载时间
            Text(
                text = "下载于 ${TimeUtils.formatDownloadTime(download.downloadTime)}",
                fontSize = 12.sp,
                color = Color.Gray
            )
        }
    }
}

 