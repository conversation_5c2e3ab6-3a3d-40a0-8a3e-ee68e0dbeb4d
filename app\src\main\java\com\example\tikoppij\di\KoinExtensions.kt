package com.example.tikoppij.di

import androidx.compose.runtime.Composable
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import org.koin.androidx.compose.koinViewModel
import org.koin.core.context.GlobalContext
import org.koin.core.parameter.ParametersDefinition
import org.koin.core.qualifier.Qualifier

/**
 * Koin 扩展函数
 * 提供便捷的依赖注入访问方式
 */

/**
 * 在 Compose 中获取 Koin 管理的 ViewModel
 */
@Composable
inline fun <reified T : ViewModel> koinViewModel(
    qualifier: Qualifier? = null,
    noinline parameters: ParametersDefinition? = null,
): T = koinViewModel(qualifier = qualifier, parameters = parameters)

/**
 * 全局获取 Koin 实例的便捷方法
 */
inline fun <reified T> getKoinInstance(): T {
    return GlobalContext.get().get()
} 