package com.example.tikoppij.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.example.tikoppij.ui.components.VideoDisplayMode
import com.example.tikoppij.utils.Constants
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import androidx.datastore.preferences.core.booleanPreferencesKey

/**
 * 用户偏好设置仓库接口
 * 定义用户偏好设置的读写操作
 */
interface UserPreferencesRepository {
    /**
     * 视频显示模式
     */
    val videoDisplayMode: Flow<VideoDisplayMode>
    
    /**
     * 更新视频显示模式
     */
    suspend fun updateVideoDisplayMode(mode: VideoDisplayMode)

    /**
     * 是否开启连播下一个视频
     */
    val autoPlayNextEnabled: Flow<Boolean>

    /**
     * 更新连播下一个视频的设置
     */
    suspend fun updateAutoPlayNextEnabled(enabled: Boolean)
    
    /**
     * 最大缓存大小（字节）
     */
    val maxCacheSize: Flow<Long>
    
    /**
     * 更新最大缓存大小
     */
    suspend fun updateMaxCacheSize(size: Long)
}

/**
 * DataStore单例扩展
 * 为Context类添加dataStore属性
 */
private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "user_preferences")

/**
 * 用户偏好设置仓库实现类
 * 使用DataStore存储用户偏好设置
 */
class UserPreferencesRepositoryImpl(private val context: Context) : UserPreferencesRepository {
    
    companion object {
        // DataStore键
        private val VIDEO_DISPLAY_MODE_KEY = stringPreferencesKey("video_display_mode")
        private val AUTO_PLAY_NEXT_KEY = booleanPreferencesKey("auto_play_next_enabled")
        private val MAX_CACHE_SIZE_KEY = longPreferencesKey("max_cache_size")
        
        // 单例模式
        @Volatile
        private var INSTANCE: UserPreferencesRepository? = null
        
        fun getInstance(context: Context): UserPreferencesRepository {
            return INSTANCE ?: synchronized(this) {
                val instance = UserPreferencesRepositoryImpl(context.applicationContext)
                INSTANCE = instance
                instance
            }
        }
    }
    
    /**
     * 获取视频显示模式
     * 默认为自动适应模式
     */
    override val videoDisplayMode: Flow<VideoDisplayMode> = context.dataStore.data
        .map { preferences ->
            val modeName = preferences[VIDEO_DISPLAY_MODE_KEY] ?: VideoDisplayMode.AUTO_ADAPT.name
            try {
                VideoDisplayMode.valueOf(modeName)
            } catch (_: IllegalArgumentException) {
                VideoDisplayMode.AUTO_ADAPT // 如果解析失败，返回默认值
            }
        }
    
    /**
     * 更新视频显示模式
     */
    override suspend fun updateVideoDisplayMode(mode: VideoDisplayMode) {
        context.dataStore.edit { preferences ->
            preferences[VIDEO_DISPLAY_MODE_KEY] = mode.name
        }
    }

    /**
     * 获取是否开启连播下一个视频的设置
     * 默认为 false (不开启)
     */
    override val autoPlayNextEnabled: Flow<Boolean> = context.dataStore.data
        .map { preferences ->
            preferences[AUTO_PLAY_NEXT_KEY] == true // 使用相等检查代替elvis进行可空布尔检查
        }

    /**
     * 更新连播下一个视频的设置
     */
    override suspend fun updateAutoPlayNextEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[AUTO_PLAY_NEXT_KEY] = enabled
        }
    }
    
    /**
     * 获取最大缓存大小（字节）
     * 默认为 500MB
     */
    override val maxCacheSize: Flow<Long> = context.dataStore.data
        .map { preferences ->
            preferences[MAX_CACHE_SIZE_KEY] ?: Constants.Cache.MIN_CACHE_SIZE // 默认500MB
        }
    
    /**
     * 更新最大缓存大小
     */
    override suspend fun updateMaxCacheSize(size: Long) {
        val validSize = size.coerceIn(Constants.Cache.MIN_CACHE_SIZE, Constants.Cache.MAX_CACHE_SIZE)
        context.dataStore.edit { preferences ->
            preferences[MAX_CACHE_SIZE_KEY] = validSize
        }
    }
} 